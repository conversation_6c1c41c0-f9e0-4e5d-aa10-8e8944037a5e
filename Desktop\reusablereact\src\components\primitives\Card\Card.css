/* Base Card Styles */
.card {
  position: relative;
  display: flex;
  flex-direction: column;
  background: var(--color-background-primary);
  transition: all 0.2s ease-in-out;
  overflow: hidden;
}

/* Card Padding */
.card--padding-none {
  padding: 0;
}

.card--padding-sm {
  padding: var(--spacing-sm);
}

.card--padding-md {
  padding: var(--spacing-md);
}

.card--padding-lg {
  padding: var(--spacing-lg);
}

.card--padding-xl {
  padding: var(--spacing-xl);
}

/* Interactive Cards */
.card--interactive {
  cursor: pointer;
}

/* Card Sub-components */
.card__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--color-border-light);
}

.card__body {
  flex: 1;
  margin-bottom: var(--spacing-md);
}

.card__footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: var(--spacing-sm);
  margin-top: auto;
  padding-top: var(--spacing-sm);
  border-top: 1px solid var(--color-border-light);
}

/* Remove margins for last elements */
.card__body:last-child,
.card__footer:last-child {
  margin-bottom: 0;
}

.card__header:last-child {
  margin-bottom: 0;
  border-bottom: none;
  padding-bottom: 0;
}

.card__footer:only-child {
  border-top: none;
  padding-top: 0;
}

/* GLASSMORPHISM THEME */
[data-theme="glassmorphism"] .card {
  border-radius: var(--border-radius-xl);
  backdrop-filter: var(--backdrop-blur-md);
  border: 1px solid var(--color-border-light);
}

[data-theme="glassmorphism"] .card--default {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
}

[data-theme="glassmorphism"] .card--elevated {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.25);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

[data-theme="glassmorphism"] .card--outlined {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.3);
  border-width: 2px;
}

[data-theme="glassmorphism"] .card--filled {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
}

[data-theme="glassmorphism"] .card--interactive:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  border-color: rgba(255, 255, 255, 0.4);
}

[data-theme="glassmorphism"] .card--selected {
  border-color: rgba(14, 165, 233, 0.5);
  box-shadow: 0 0 0 2px rgba(14, 165, 233, 0.2);
}

[data-theme="glassmorphism"] .card__header {
  border-bottom-color: rgba(255, 255, 255, 0.2);
}

[data-theme="glassmorphism"] .card__footer {
  border-top-color: rgba(255, 255, 255, 0.2);
}

/* MINIMAL THEME */
[data-theme="minimal"] .card {
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--color-border-light);
}

[data-theme="minimal"] .card--default {
  background: var(--color-background-primary);
  border-color: var(--color-border-light);
}

[data-theme="minimal"] .card--elevated {
  background: var(--color-background-primary);
  border-color: var(--color-border-light);
  box-shadow: var(--shadow-lg);
}

[data-theme="minimal"] .card--outlined {
  background: var(--color-background-primary);
  border-color: var(--color-border-medium);
  border-width: 2px;
}

[data-theme="minimal"] .card--filled {
  background: var(--color-background-secondary);
  border-color: var(--color-border-light);
}

[data-theme="minimal"] .card--interactive:hover {
  box-shadow: var(--shadow-xl);
  border-color: var(--color-border-medium);
}

[data-theme="minimal"] .card--selected {
  border-color: var(--color-primary-500);
  box-shadow: 0 0 0 2px var(--color-primary-100);
}

[data-theme="minimal"] .card__header {
  border-bottom-color: var(--color-border-light);
}

[data-theme="minimal"] .card__footer {
  border-top-color: var(--color-border-light);
}

/* NEUMORPHISM THEME */
[data-theme="neumorphism"] .card {
  border-radius: var(--border-radius-2xl);
  border: none;
}

[data-theme="neumorphism"] .card--default {
  background: var(--color-background-primary);
  box-shadow: var(--shadow-md);
}

[data-theme="neumorphism"] .card--elevated {
  background: var(--color-background-primary);
  box-shadow: var(--shadow-lg);
}

[data-theme="neumorphism"] .card--outlined {
  background: var(--color-background-primary);
  box-shadow: var(--shadow-inner);
}

[data-theme="neumorphism"] .card--filled {
  background: var(--color-background-secondary);
  box-shadow: var(--shadow-md);
}

[data-theme="neumorphism"] .card--interactive:hover {
  box-shadow: var(--shadow-xl);
}

[data-theme="neumorphism"] .card--interactive:active {
  box-shadow: var(--shadow-inner);
}

[data-theme="neumorphism"] .card--selected {
  box-shadow: var(--shadow-inner), 0 0 0 2px var(--color-primary-300);
}

[data-theme="neumorphism"] .card__header {
  border-bottom-color: var(--color-border-light);
}

[data-theme="neumorphism"] .card__footer {
  border-top-color: var(--color-border-light);
}

/* BRUTALIST THEME */
[data-theme="brutalist"] .card {
  border-radius: var(--border-radius-none);
  border: 3px solid var(--color-border-heavy);
}

[data-theme="brutalist"] .card--default {
  background: var(--color-background-primary);
  border-color: var(--color-neutral-900);
  box-shadow: var(--shadow-md);
}

[data-theme="brutalist"] .card--elevated {
  background: var(--color-background-primary);
  border-color: var(--color-neutral-900);
  box-shadow: var(--shadow-lg);
}

[data-theme="brutalist"] .card--outlined {
  background: var(--color-background-primary);
  border-color: var(--color-neutral-900);
  border-width: 4px;
}

[data-theme="brutalist"] .card--filled {
  background: var(--color-primary-500);
  border-color: var(--color-neutral-900);
  color: var(--color-text-inverse);
}

[data-theme="brutalist"] .card--interactive:hover {
  transform: translate(-3px, -3px);
  box-shadow: var(--shadow-xl);
}

[data-theme="brutalist"] .card--interactive:active {
  transform: translate(0, 0);
  box-shadow: var(--shadow-sm);
}

[data-theme="brutalist"] .card--selected {
  border-color: var(--color-primary-500);
  border-width: 4px;
}

[data-theme="brutalist"] .card__header {
  border-bottom-color: var(--color-neutral-900);
  border-bottom-width: 2px;
}

[data-theme="brutalist"] .card__footer {
  border-top-color: var(--color-neutral-900);
  border-top-width: 2px;
}

[data-theme="brutalist"] .card--filled .card__header {
  border-bottom-color: rgba(255, 255, 255, 0.3);
}

[data-theme="brutalist"] .card--filled .card__footer {
  border-top-color: rgba(255, 255, 255, 0.3);
}