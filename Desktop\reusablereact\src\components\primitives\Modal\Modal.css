/* Modal Overlay */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: var(--spacing-md);
  animation: modal-overlay-enter 0.2s ease-out;
}

@keyframes modal-overlay-enter {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Modal Container */
.modal {
  background: var(--color-background-primary);
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-xl);
  display: flex;
  flex-direction: column;
  max-height: calc(100vh - 2rem);
  overflow: hidden;
  position: relative;
  animation: modal-enter 0.3s ease-out;
  outline: none;
}

@keyframes modal-enter {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Modal Sizes */
.modal--sm {
  width: 100%;
  max-width: 400px;
}

.modal--md {
  width: 100%;
  max-width: 500px;
}

.modal--lg {
  width: 100%;
  max-width: 700px;
}

.modal--xl {
  width: 100%;
  max-width: 900px;
}

.modal--full {
  width: 100%;
  height: 100%;
  max-width: none;
  max-height: none;
  border-radius: 0;
}

/* Modal Header */
.modal__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--color-border-light);
  flex-shrink: 0;
}

.modal__title {
  margin: 0;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  line-height: var(--line-height-tight);
}

.modal__close-button {
  background: none;
  border: none;
  cursor: pointer;
  color: var(--color-text-secondary);
  padding: var(--spacing-xs);
  border-radius: var(--border-radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease-in-out;
  margin-left: var(--spacing-md);
}

.modal__close-button:hover {
  color: var(--color-text-primary);
  background: var(--color-background-secondary);
}

.modal__close-button:focus {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
}

/* Modal Body */
.modal__body {
  padding: var(--spacing-lg);
  overflow-y: auto;
  flex: 1;
}

/* Modal Footer */
.modal__footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg);
  border-top: 1px solid var(--color-border-light);
  flex-shrink: 0;
}

/* Remove borders when components are used alone */
.modal__header:only-child {
  border-bottom: none;
}

.modal__footer:only-child {
  border-top: none;
}

.modal__body:first-child {
  padding-top: var(--spacing-lg);
}

.modal__body:last-child {
  padding-bottom: var(--spacing-lg);
}

/* Responsive Design */
@media (max-width: 640px) {
  .modal-overlay {
    padding: var(--spacing-sm);
    align-items: flex-end;
  }
  
  .modal {
    width: 100%;
    max-width: none;
    border-radius: var(--border-radius-xl) var(--border-radius-xl) 0 0;
    animation: modal-enter-mobile 0.3s ease-out;
  }
  
  .modal--full {
    height: 100%;
    border-radius: 0;
  }
  
  @keyframes modal-enter-mobile {
    from {
      opacity: 0;
      transform: translateY(100%);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .modal__header,
  .modal__body,
  .modal__footer {
    padding: var(--spacing-md);
  }
  
  .modal__title {
    font-size: var(--font-size-lg);
  }
}

/* GLASSMORPHISM THEME */
[data-theme="glassmorphism"] .modal-overlay {
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: var(--backdrop-blur-sm);
}

[data-theme="glassmorphism"] .modal {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: var(--backdrop-blur-lg);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

[data-theme="glassmorphism"] .modal__header {
  border-bottom-color: rgba(255, 255, 255, 0.2);
}

[data-theme="glassmorphism"] .modal__footer {
  border-top-color: rgba(255, 255, 255, 0.2);
}

[data-theme="glassmorphism"] .modal__close-button:hover {
  background: rgba(255, 255, 255, 0.1);
}

[data-theme="glassmorphism"] .modal__close-button:focus {
  outline-color: rgba(14, 165, 233, 0.6);
}

/* MINIMAL THEME */
[data-theme="minimal"] .modal-overlay {
  background: rgba(0, 0, 0, 0.4);
}

[data-theme="minimal"] .modal {
  background: var(--color-background-primary);
  border: 1px solid var(--color-border-light);
  box-shadow: var(--shadow-2xl);
}

[data-theme="minimal"] .modal__header {
  border-bottom-color: var(--color-border-light);
}

[data-theme="minimal"] .modal__footer {
  border-top-color: var(--color-border-light);
}

[data-theme="minimal"] .modal__close-button:hover {
  background: var(--color-background-secondary);
}

[data-theme="minimal"] .modal__close-button:focus {
  outline-color: var(--color-primary-500);
}

/* NEUMORPHISM THEME */
[data-theme="neumorphism"] .modal-overlay {
  background: rgba(0, 0, 0, 0.3);
}

[data-theme="neumorphism"] .modal {
  background: var(--color-background-primary);
  border: none;
  box-shadow: var(--shadow-2xl);
  border-radius: var(--border-radius-2xl);
}

[data-theme="neumorphism"] .modal__header {
  border-bottom-color: var(--color-border-light);
}

[data-theme="neumorphism"] .modal__footer {
  border-top-color: var(--color-border-light);
}

[data-theme="neumorphism"] .modal__close-button {
  border-radius: var(--border-radius-full);
}

[data-theme="neumorphism"] .modal__close-button:hover {
  background: var(--color-background-secondary);
  box-shadow: var(--shadow-inner);
}

[data-theme="neumorphism"] .modal__close-button:focus {
  outline-color: var(--color-primary-500);
  box-shadow: var(--shadow-inner), 0 0 0 2px var(--color-primary-300);
}

/* BRUTALIST THEME */
[data-theme="brutalist"] .modal-overlay {
  background: rgba(0, 0, 0, 0.6);
}

[data-theme="brutalist"] .modal {
  background: var(--color-background-primary);
  border: 4px solid var(--color-neutral-900);
  border-radius: var(--border-radius-none);
  box-shadow: var(--shadow-xl);
}

[data-theme="brutalist"] .modal--full {
  border: none;
}

[data-theme="brutalist"] .modal__header {
  border-bottom: 3px solid var(--color-neutral-900);
}

[data-theme="brutalist"] .modal__footer {
  border-top: 3px solid var(--color-neutral-900);
}

[data-theme="brutalist"] .modal__close-button {
  border-radius: var(--border-radius-none);
  border: 2px solid var(--color-neutral-900);
  background: var(--color-background-primary);
}

[data-theme="brutalist"] .modal__close-button:hover {
  background: var(--color-neutral-900);
  color: var(--color-background-primary);
  transform: translate(-2px, -2px);
  box-shadow: 2px 2px 0 var(--color-neutral-900);
}

[data-theme="brutalist"] .modal__close-button:active {
  transform: translate(0, 0);
  box-shadow: none;
}

[data-theme="brutalist"] .modal__close-button:focus {
  outline: 3px solid var(--color-primary-500);
  outline-offset: 2px;
}

/* Animation overrides for brutalist theme */
[data-theme="brutalist"] .modal {
  animation: modal-enter-brutalist 0.3s ease-out;
}

@keyframes modal-enter-brutalist {
  from {
    opacity: 0;
    transform: translate(20px, 20px);
  }
  to {
    opacity: 1;
    transform: translate(0, 0);
  }
}

@media (max-width: 640px) {
  [data-theme="brutalist"] .modal {
    animation: modal-enter-mobile-brutalist 0.3s ease-out;
  }
  
  @keyframes modal-enter-mobile-brutalist {
    from {
      opacity: 0;
      transform: translateY(100%);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}