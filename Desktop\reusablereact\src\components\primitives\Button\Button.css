/* Base Button Styles */
.btn {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  font-family: var(--font-family-sans);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-tight);
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  user-select: none;
  white-space: nowrap;
  overflow: hidden;
}

.btn:focus {
  outline: none;
}

.btn:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* Button Sizes */
.btn--xs {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--font-size-xs);
  min-height: 1.5rem;
}

.btn--sm {
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-size-sm);
  min-height: 2rem;
}

.btn--md {
  padding: var(--spacing-sm) var(--spacing-lg);
  font-size: var(--font-size-base);
  min-height: 2.5rem;
}

.btn--lg {
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--font-size-lg);
  min-height: 3rem;
}

.btn--xl {
  padding: var(--spacing-lg) var(--spacing-2xl);
  font-size: var(--font-size-xl);
  min-height: 3.5rem;
}

/* Full Width */
.btn--full-width {
  width: 100%;
}

/* Loading State */
.btn--loading {
  color: transparent;
}

.btn__spinner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.btn__spinner-icon {
  width: 1em;
  height: 1em;
  animation: spin 1s linear infinite;
}

.btn__spinner-circle {
  stroke: currentColor;
  stroke-linecap: round;
  stroke-dasharray: 31.416;
  stroke-dashoffset: 31.416;
  animation: dash 2s ease-in-out infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes dash {
  0% {
    stroke-dasharray: 1, 150;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -35;
  }
  100% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -124;
  }
}

/* Icon Styles */
.btn__icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn__icon svg {
  width: 1em;
  height: 1em;
}

/* GLASSMORPHISM THEME */
[data-theme="glassmorphism"] .btn {
  border-radius: var(--border-radius-lg);
  backdrop-filter: var(--backdrop-blur-md);
  border: 1px solid var(--color-border-light);
}

[data-theme="glassmorphism"] .btn--primary {
  background: rgba(14, 165, 233, 0.2);
  color: var(--color-primary-700);
  border-color: rgba(14, 165, 233, 0.3);
}

[data-theme="glassmorphism"] .btn--primary:hover:not(:disabled) {
  background: rgba(14, 165, 233, 0.3);
  border-color: rgba(14, 165, 233, 0.4);
  box-shadow: 0 8px 32px rgba(14, 165, 233, 0.2);
  transform: translateY(-1px);
}

[data-theme="glassmorphism"] .btn--secondary {
  background: rgba(217, 70, 239, 0.2);
  color: var(--color-secondary-700);
  border-color: rgba(217, 70, 239, 0.3);
}

[data-theme="glassmorphism"] .btn--secondary:hover:not(:disabled) {
  background: rgba(217, 70, 239, 0.3);
  border-color: rgba(217, 70, 239, 0.4);
  box-shadow: 0 8px 32px rgba(217, 70, 239, 0.2);
  transform: translateY(-1px);
}

[data-theme="glassmorphism"] .btn--outline {
  background: rgba(255, 255, 255, 0.1);
  color: var(--color-text-primary);
  border-color: var(--color-border-medium);
}

[data-theme="glassmorphism"] .btn--outline:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.2);
  border-color: var(--color-border-heavy);
  box-shadow: var(--shadow-lg);
}

[data-theme="glassmorphism"] .btn--ghost {
  background: transparent;
  color: var(--color-text-primary);
  border-color: transparent;
}

[data-theme="glassmorphism"] .btn--ghost:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.1);
  border-color: var(--color-border-light);
}

[data-theme="glassmorphism"] .btn--danger {
  background: rgba(239, 68, 68, 0.2);
  color: var(--color-error);
  border-color: rgba(239, 68, 68, 0.3);
}

[data-theme="glassmorphism"] .btn--danger:hover:not(:disabled) {
  background: rgba(239, 68, 68, 0.3);
  border-color: rgba(239, 68, 68, 0.4);
  box-shadow: 0 8px 32px rgba(239, 68, 68, 0.2);
}

/* MINIMAL THEME */
[data-theme="minimal"] .btn {
  border-radius: var(--border-radius-md);
  border: 1px solid transparent;
}

[data-theme="minimal"] .btn--primary {
  background: var(--color-primary-600);
  color: var(--color-text-inverse);
}

[data-theme="minimal"] .btn--primary:hover:not(:disabled) {
  background: var(--color-primary-700);
  box-shadow: var(--shadow-md);
}

[data-theme="minimal"] .btn--secondary {
  background: var(--color-secondary-600);
  color: var(--color-text-inverse);
}

[data-theme="minimal"] .btn--secondary:hover:not(:disabled) {
  background: var(--color-secondary-700);
  box-shadow: var(--shadow-md);
}

[data-theme="minimal"] .btn--outline {
  background: transparent;
  color: var(--color-primary-600);
  border-color: var(--color-primary-600);
}

[data-theme="minimal"] .btn--outline:hover:not(:disabled) {
  background: var(--color-primary-50);
  color: var(--color-primary-700);
}

[data-theme="minimal"] .btn--ghost {
  background: transparent;
  color: var(--color-text-primary);
}

[data-theme="minimal"] .btn--ghost:hover:not(:disabled) {
  background: var(--color-neutral-100);
}

[data-theme="minimal"] .btn--danger {
  background: var(--color-error);
  color: var(--color-text-inverse);
}

[data-theme="minimal"] .btn--danger:hover:not(:disabled) {
  background: #dc2626;
  box-shadow: var(--shadow-md);
}

/* NEUMORPHISM THEME */
[data-theme="neumorphism"] .btn {
  border-radius: var(--border-radius-xl);
  border: none;
  background: var(--color-background-primary);
}

[data-theme="neumorphism"] .btn--primary {
  background: var(--color-background-primary);
  color: var(--color-primary-600);
  box-shadow: var(--shadow-md);
}

[data-theme="neumorphism"] .btn--primary:hover:not(:disabled) {
  box-shadow: var(--shadow-lg);
}

[data-theme="neumorphism"] .btn--primary:active {
  box-shadow: var(--shadow-inner);
}

[data-theme="neumorphism"] .btn--secondary {
  background: var(--color-background-primary);
  color: var(--color-secondary-600);
  box-shadow: var(--shadow-md);
}

[data-theme="neumorphism"] .btn--secondary:hover:not(:disabled) {
  box-shadow: var(--shadow-lg);
}

[data-theme="neumorphism"] .btn--outline {
  background: var(--color-background-primary);
  color: var(--color-text-primary);
  box-shadow: var(--shadow-inner);
}

[data-theme="neumorphism"] .btn--outline:hover:not(:disabled) {
  box-shadow: var(--shadow-md);
}

[data-theme="neumorphism"] .btn--ghost {
  background: transparent;
  color: var(--color-text-primary);
  box-shadow: none;
}

[data-theme="neumorphism"] .btn--ghost:hover:not(:disabled) {
  box-shadow: var(--shadow-sm);
}

[data-theme="neumorphism"] .btn--danger {
  background: var(--color-background-primary);
  color: var(--color-error);
  box-shadow: var(--shadow-md);
}

/* BRUTALIST THEME */
[data-theme="brutalist"] .btn {
  border-radius: var(--border-radius-none);
  border: 3px solid var(--color-border-heavy);
  font-weight: var(--font-weight-bold);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

[data-theme="brutalist"] .btn--primary {
  background: var(--color-primary-500);
  color: var(--color-text-inverse);
  border-color: var(--color-neutral-900);
  box-shadow: var(--shadow-md);
}

[data-theme="brutalist"] .btn--primary:hover:not(:disabled) {
  transform: translate(-2px, -2px);
  box-shadow: var(--shadow-lg);
}

[data-theme="brutalist"] .btn--primary:active {
  transform: translate(0, 0);
  box-shadow: var(--shadow-sm);
}

[data-theme="brutalist"] .btn--secondary {
  background: var(--color-secondary-500);
  color: var(--color-text-inverse);
  border-color: var(--color-neutral-900);
  box-shadow: var(--shadow-md);
}

[data-theme="brutalist"] .btn--secondary:hover:not(:disabled) {
  transform: translate(-2px, -2px);
  box-shadow: var(--shadow-lg);
}

[data-theme="brutalist"] .btn--outline {
  background: var(--color-background-primary);
  color: var(--color-neutral-900);
  border-color: var(--color-neutral-900);
  box-shadow: var(--shadow-md);
}

[data-theme="brutalist"] .btn--outline:hover:not(:disabled) {
  background: var(--color-neutral-900);
  color: var(--color-text-inverse);
  transform: translate(-2px, -2px);
  box-shadow: var(--shadow-lg);
}

[data-theme="brutalist"] .btn--ghost {
  background: transparent;
  color: var(--color-neutral-900);
  border-color: transparent;
  box-shadow: none;
}

[data-theme="brutalist"] .btn--ghost:hover:not(:disabled) {
  background: var(--color-neutral-900);
  color: var(--color-text-inverse);
  border-color: var(--color-neutral-900);
  box-shadow: var(--shadow-md);
}

[data-theme="brutalist"] .btn--danger {
  background: var(--color-error);
  color: var(--color-text-inverse);
  border-color: var(--color-neutral-900);
  box-shadow: var(--shadow-md);
}

[data-theme="brutalist"] .btn--danger:hover:not(:disabled) {
  transform: translate(-2px, -2px);
  box-shadow: var(--shadow-lg);
}