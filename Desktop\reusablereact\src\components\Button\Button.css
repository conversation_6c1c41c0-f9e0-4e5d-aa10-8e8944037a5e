.btn {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.375rem;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  outline: none;
  border: none;
  gap: 0.5rem;
}

/* Sizes */
.btn-sm {
  height: 2rem;
  padding: 0 0.75rem;
  font-size: 0.875rem;
}

.btn-md {
  height: 2.5rem;
  padding: 0 1rem;
  font-size: 1rem;
}

.btn-lg {
  height: 3rem;
  padding: 0 1.5rem;
  font-size: 1.125rem;
}

/* Variants */
.btn-primary {
  background-color: var(--primary-color, #3b82f6);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--primary-hover, #2563eb);
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.btn-primary:active:not(:disabled) {
  background-color: var(--primary-active, #1d4ed8);
  transform: translateY(0);
  box-shadow: none;
}

.btn-secondary {
  background-color: var(--secondary-color, #6b7280);
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--secondary-hover, #4b5563);
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.btn-outline {
  background-color: transparent;
  border: 1px solid var(--primary-color, #3b82f6);
  color: var(--primary-color, #3b82f6);
}

.btn-outline:hover:not(:disabled) {
  background-color: rgba(59, 130, 246, 0.05);
}

.btn-ghost {
  background-color: transparent;
  color: var(--text-color, #374151);
}

.btn-ghost:hover:not(:disabled) {
  background-color: rgba(0, 0, 0, 0.05);
}

/* States */
.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-loading {
  cursor: wait;
}

.btn-spinner {
  position: absolute;
  width: 1rem;
  height: 1rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 0.8s linear infinite;
}

.btn-loading .btn-text {
  opacity: 0;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .btn-ghost {
    color: var(--text-color-dark, #e5e7eb);
  }
  
  .btn-ghost:hover:not(:disabled) {
    background-color: rgba(255, 255, 255, 0.1);
  }
  
  .btn-outline {
    border-color: var(--primary-color-dark, #60a5fa);
    color: var(--primary-color-dark, #60a5fa);
  }
}