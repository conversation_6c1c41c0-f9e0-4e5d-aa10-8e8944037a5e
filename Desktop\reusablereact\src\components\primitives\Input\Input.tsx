import React, { forwardRef, useState } from 'react';
import { useTheme } from '../../../themes';
import './Input.css';

export interface InputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'> {
  /** Input variant */
  variant?: 'default' | 'filled' | 'outlined';
  /** Input size */
  size?: 'sm' | 'md' | 'lg';
  /** Input state */
  state?: 'default' | 'error' | 'success' | 'warning';
  /** Label text */
  label?: string;
  /** Helper text */
  helperText?: string;
  /** Error message */
  errorMessage?: string;
  /** Success message */
  successMessage?: string;
  /** Warning message */
  warningMessage?: string;
  /** Whether the input is required */
  required?: boolean;
  /** Left icon */
  leftIcon?: React.ReactNode;
  /** Right icon */
  rightIcon?: React.ReactNode;
  /** Whether to show password toggle (for password inputs) */
  showPasswordToggle?: boolean;
  /** Full width */
  fullWidth?: boolean;
}

export const Input = forwardRef<HTMLInputElement, InputProps>((
  {
    variant = 'default',
    size = 'md',
    state = 'default',
    label,
    helperText,
    errorMessage,
    successMessage,
    warningMessage,
    required = false,
    leftIcon,
    rightIcon,
    showPasswordToggle = false,
    fullWidth = false,
    type = 'text',
    className = '',
    disabled = false,
    ...props
  },
  ref
) => {
  const { themeName } = useTheme();
  const [showPassword, setShowPassword] = useState(false);
  const [isFocused, setIsFocused] = useState(false);

  // Determine the actual input type
  const inputType = type === 'password' && showPassword ? 'text' : type;

  // Determine the current state based on props
  const currentState = errorMessage ? 'error' : 
                      successMessage ? 'success' : 
                      warningMessage ? 'warning' : 
                      state;

  // Get the appropriate message
  const message = errorMessage || successMessage || warningMessage || helperText;

  const containerClasses = [
    'input-container',
    fullWidth && 'input-container--full-width',
    disabled && 'input-container--disabled'
  ].filter(Boolean).join(' ');

  const wrapperClasses = [
    'input-wrapper',
    `input-wrapper--${variant}`,
    `input-wrapper--${size}`,
    `input-wrapper--${currentState}`,
    `input-wrapper--${themeName}`,
    isFocused && 'input-wrapper--focused',
    disabled && 'input-wrapper--disabled',
    (leftIcon || rightIcon) && 'input-wrapper--with-icons'
  ].filter(Boolean).join(' ');

  const inputClasses = [
    'input',
    `input--${size}`,
    leftIcon && 'input--with-left-icon',
    (rightIcon || (type === 'password' && showPasswordToggle)) && 'input--with-right-icon',
    className
  ].filter(Boolean).join(' ');

  const handlePasswordToggle = () => {
    setShowPassword(!showPassword);
  };

  const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
    setIsFocused(true);
    props.onFocus?.(e);
  };

  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    setIsFocused(false);
    props.onBlur?.(e);
  };

  return (
    <div className={containerClasses}>
      {label && (
        <label className="input-label">
          {label}
          {required && <span className="input-label__required">*</span>}
        </label>
      )}
      
      <div className={wrapperClasses}>
        {leftIcon && (
          <div className="input-icon input-icon--left">
            {leftIcon}
          </div>
        )}
        
        <input
          ref={ref}
          type={inputType}
          className={inputClasses}
          disabled={disabled}
          onFocus={handleFocus}
          onBlur={handleBlur}
          {...props}
        />
        
        {(rightIcon || (type === 'password' && showPasswordToggle)) && (
          <div className="input-icon input-icon--right">
            {type === 'password' && showPasswordToggle ? (
              <button
                type="button"
                className="input-password-toggle"
                onClick={handlePasswordToggle}
                tabIndex={-1}
                aria-label={showPassword ? 'Hide password' : 'Show password'}
              >
                {showPassword ? (
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"/>
                    <line x1="1" y1="1" x2="23" y2="23"/>
                  </svg>
                ) : (
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                    <circle cx="12" cy="12" r="3"/>
                  </svg>
                )}
              </button>
            ) : rightIcon}
          </div>
        )}
      </div>
      
      {message && (
        <div className={`input-message input-message--${currentState}`}>
          {message}
        </div>
      )}
    </div>
  );
});

Input.displayName = 'Input';