import * as eslint from 'eslint';

declare const meta: {
    name: string;
};
declare const rules: {
    'react-compiler': eslint.Rule.RuleModule;
};
declare const configs: {
    recommended: {
        plugins: {
            'react-compiler': {
                rules: {
                    'react-compiler': eslint.Rule.RuleModule;
                };
            };
        };
        rules: {
            'react-compiler/react-compiler': "error";
        };
    };
};

export { configs, meta, rules };
