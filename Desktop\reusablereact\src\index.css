:root {
  /* Default fallback variables */
  --color-primary: #3b82f6;
  --color-secondary: #6366f1;
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-danger: #ef4444;
  --color-info: #06b6d4;
  --color-background: #f9fafb;
  --color-surface: #ffffff;
  --color-text: #1f2937;
  --color-text-secondary: #6b7280;
  --color-border: #e5e7eb;
  --color-divider: #f3f4f6;
  --color-focus: rgba(59, 130, 246, 0.5);
  --color-overlay: rgba(0, 0, 0, 0.5);
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;
  --spacing-4xl: 6rem;
  
  /* Typography */
  --typography-fontFamily-base: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  --typography-fontFamily-heading: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  --typography-fontFamily-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  
  --typography-fontSize-xs: 0.75rem;
  --typography-fontSize-sm: 0.875rem;
  --typography-fontSize-md: 1rem;
  --typography-fontSize-lg: 1.125rem;
  --typography-fontSize-xl: 1.25rem;
  --typography-fontSize-2xl: 1.5rem;
  --typography-fontSize-3xl: 1.875rem;
  --typography-fontSize-4xl: 2.25rem;
  
  --typography-fontWeight-light: 300;
  --typography-fontWeight-normal: 400;
  --typography-fontWeight-medium: 500;
  --typography-fontWeight-semibold: 600;
  --typography-fontWeight-bold: 700;
  
  --typography-lineHeight-none: 1;
  --typography-lineHeight-tight: 1.25;
  --typography-lineHeight-normal: 1.5;
  --typography-lineHeight-relaxed: 1.75;
  --typography-lineHeight-loose: 2;
  
  /* Shadows */
  --shadow-none: none;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  
  /* Border Radius */
  --radius-none: 0;
  --radius-sm: 0.125rem;
  --radius-md: 0.25rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-full: 9999px;
  
  /* Effects */
  --blur-sm: 4px;
  --blur-md: 8px;
  --blur-lg: 16px;
  --blur-xl: 24px;
}

/* Base styles */
body {
  margin: 0;
  font-family: var(--typography-fontFamily-base);
  font-size: var(--typography-fontSize-md);
  line-height: var(--typography-lineHeight-normal);
  color: var(--color-text);
  background-color: var(--color-background);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--typography-fontFamily-heading);
  margin-top: 0;
  line-height: var(--typography-lineHeight-tight);
  color: var(--color-text);
}

h1 {
  font-size: var(--typography-fontSize-4xl);
  font-weight: var(--typography-fontWeight-bold);
}

h2 {
  font-size: var(--typography-fontSize-3xl);
  font-weight: var(--typography-fontWeight-bold);
}

h3 {
  font-size: var(--typography-fontSize-2xl);
  font-weight: var(--typography-fontWeight-semibold);
}

h4 {
  font-size: var(--typography-fontSize-xl);
  font-weight: var(--typography-fontWeight-semibold);
}

p {
  margin-top: 0;
  margin-bottom: var(--spacing-md);
}

code {
  font-family: var(--typography-fontFamily-mono);
  background-color: rgba(0, 0, 0, 0.05);
  padding: 0.2em 0.4em;
  border-radius: var(--radius-sm);
}

/* Glassmorphism theme overrides */
[data-theme="glassmorphism"] {
  --color-surface: rgba(255, 255, 255, 0.1);
  --color-border: rgba(255, 255, 255, 0.2);
}

[data-theme="glassmorphism"][data-mode="dark"] {
  --color-surface: rgba(255, 255, 255, 0.05);
  --color-border: rgba(255, 255, 255, 0.1);
}

/* Dark mode overrides */
[data-mode="dark"] {
  --color-text: #f9fafb;
  --color-text-secondary: #9ca3af;
  --color-background: #111827;
  --color-surface: #1f2937;
  --color-border: #374151;
  --color-divider: #374151;
}

/* Smooth transitions for all interactive elements */
* {
  transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease, box-shadow 0.2s ease;
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-background);
}

::-webkit-scrollbar-thumb {
  background: var(--color-border);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-text-secondary);
}

