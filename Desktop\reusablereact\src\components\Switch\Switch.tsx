import React from 'react';
import './Switch.css';

export interface SwitchProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'type' | 'size'> {
  /**
   * Label for the switch
   */
  label?: string;
  /**
   * Helper text to display below the switch
   */
  helperText?: string;
  /**
   * Error message to display
   */
  error?: string;
  /**
   * Size of the switch
   */
  size?: 'sm' | 'md' | 'lg';
}

export const Switch = React.forwardRef<HTMLInputElement, SwitchProps>(
  (
    {
      className = '',
      label,
      helperText,
      error,
      size = 'md',
      id,
      disabled,
      ...props
    },
    ref
  ) => {
    const inputId = id || `switch-${Math.random().toString(36).substring(2, 9)}`;
    
    const baseClass = 'switch-wrapper';
    const sizeClass = `switch-size-${size}`;
    const errorClass = error ? 'switch-error' : '';
    const disabledClass = disabled ? 'switch-disabled' : '';
    
    const wrapperClasses = [
      baseClass,
      sizeClass,
      errorClass,
      disabledClass,
      className
    ].filter(Boolean).join(' ');

    return (
      <div className={wrapperClasses}>
        <div className="switch-container">
          <input
            ref={ref}
            id={inputId}
            type="checkbox"
            role="switch"
            className="switch-input"
            aria-invalid={!!error}
            aria-describedby={error ? `${inputId}-error` : helperText ? `${inputId}-helper` : undefined}
            disabled={disabled}
            {...props}
          />
          <div className="switch-track" aria-hidden="true">
            <div className="switch-thumb"></div>
          </div>
          
          {label && (
            <label htmlFor={inputId} className="switch-label">
              {label}
            </label>
          )}
        </div>
        
        {(helperText || error) && (
          <div 
            className={`switch-message ${error ? 'switch-message-error' : ''}`}
            id={error ? `${inputId}-error` : `${inputId}-helper`}
          >
            {error || helperText}
          </div>
        )}
      </div>
    );
  }
);

Switch.displayName = 'Switch';
