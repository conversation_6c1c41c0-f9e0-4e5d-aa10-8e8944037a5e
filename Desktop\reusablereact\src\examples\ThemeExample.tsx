import React from 'react';
import { ThemeSwitcher } from '../components/ThemeSwitcher';
import { Card, CardHeader, CardBody, CardFooter } from '../components/primitives/Card';
import { Button } from '../components/primitives/Button';
import { useTheme } from '../themes';
import './ThemeExample.css';

export const ThemeExample: React.FC = () => {
  const { themeName, isDarkMode } = useTheme();
  
  return (
    <div className="theme-example">
      <h2>Theme System</h2>
      
      <div className="theme-controls">
        <ThemeSwitcher />
      </div>
      
      <div className="theme-status">
        <h3>Current Theme: {themeName.charAt(0).toUpperCase() + themeName.slice(1)} ({isDarkMode ? 'Dark' : 'Light'})</h3>
      </div>

      <div className="theme-preview">
        
        <div className="color-palette">
          <h4>Color Palette</h4>
          <div className="color-grid">
            <div className="color-item" style={{ backgroundColor: 'var(--color-primary)' }}>
              <span>Primary</span>
            </div>
            <div className="color-item" style={{ backgroundColor: 'var(--color-secondary)' }}>
              <span>Secondary</span>
            </div>
            <div className="color-item" style={{ backgroundColor: 'var(--color-success)' }}>
              <span>Success</span>
            </div>
            <div className="color-item" style={{ backgroundColor: 'var(--color-warning)' }}>
              <span>Warning</span>
            </div>
            <div className="color-item" style={{ backgroundColor: 'var(--color-danger)' }}>
              <span>Danger</span>
            </div>
            <div className="color-item" style={{ backgroundColor: 'var(--color-info)' }}>
              <span>Info</span>
            </div>
          </div>
        </div>
        
        <div className="component-preview">
          <h4>Component Preview</h4>
          <Card>
            <CardHeader>Card Component</CardHeader>
            <CardBody>
              <p>This card demonstrates how components adapt to the current theme.</p>
              <div className="button-group">
                <Button variant="primary">Primary</Button>
                <Button variant="secondary">Secondary</Button>
                <Button variant="outline">Outline</Button>
                <Button variant="danger">Danger</Button>
              </div>
            </CardBody>
            <CardFooter>
              <Button variant="text">Cancel</Button>
              <Button>Submit</Button>
            </CardFooter>
          </Card>
        </div>
        
        <div className="typography-preview">
          <h4>Typography</h4>
          <div className="typography-samples">
            <h1>Heading 1</h1>
            <h2>Heading 2</h2>
            <h3>Heading 3</h3>
            <h4>Heading 4</h4>
            <p>Regular paragraph text. Lorem ipsum dolor sit amet, consectetur adipiscing elit.</p>
            <p><small>Small text for less important information.</small></p>
            <p><strong>Bold text for emphasis.</strong></p>
            <p><code>Monospace text for code.</code></p>
          </div>
        </div>
      </div>
    </div>
  );
};