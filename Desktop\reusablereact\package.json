{"name": "your-component-library", "version": "0.1.0", "description": "A modern React component library with accessibility and performance in mind", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist"], "sideEffects": false, "scripts": {"dev": "vite", "build": "tsc -b && vite build", "build:lib": "rollup -c", "lint": "eslint .", "preview": "vite preview", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "test": "vitest run", "test:watch": "vitest"}, "peerDependencies": {"react": ">=17.0.0", "react-dom": ">=17.0.0"}, "devDependencies": {"@testing-library/react": "^14.0.0", "@testing-library/jest-dom": "^6.1.4", "vitest": "^0.34.6", "jsdom": "^22.1.0"}}