/* Base Input Styles */
.input-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.input-container--full-width {
  width: 100%;
}

.input-container--disabled {
  opacity: 0.6;
  pointer-events: none;
}

/* Label Styles */
.input-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-xs);
}

.input-label__required {
  color: var(--color-error-500);
  margin-left: 2px;
}

/* Input Wrapper */
.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  transition: all 0.2s ease-in-out;
  background: var(--color-background-primary);
  border: 1px solid var(--color-border-light);
}

.input-wrapper--with-icons {
  padding: 0;
}

/* Input Sizes */
.input-wrapper--sm {
  height: 2rem;
  border-radius: var(--border-radius-md);
  padding: 0 var(--spacing-sm);
}

.input-wrapper--md {
  height: 2.5rem;
  border-radius: var(--border-radius-lg);
  padding: 0 var(--spacing-md);
}

.input-wrapper--lg {
  height: 3rem;
  border-radius: var(--border-radius-lg);
  padding: 0 var(--spacing-lg);
}

/* Input Element */
.input {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  font-size: var(--font-size-md);
  color: var(--color-text-primary);
  font-family: var(--font-family-sans);
}

.input--sm {
  font-size: var(--font-size-sm);
}

.input--lg {
  font-size: var(--font-size-lg);
}

.input--with-left-icon {
  padding-left: 0;
}

.input--with-right-icon {
  padding-right: 0;
}

.input::placeholder {
  color: var(--color-text-tertiary);
}

/* Input Icons */
.input-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-text-secondary);
  pointer-events: none;
}

.input-icon--left {
  padding-left: var(--spacing-sm);
  padding-right: var(--spacing-xs);
}

.input-icon--right {
  padding-left: var(--spacing-xs);
  padding-right: var(--spacing-sm);
}

.input-wrapper--sm .input-icon--left,
.input-wrapper--sm .input-icon--right {
  padding-left: var(--spacing-xs);
  padding-right: var(--spacing-xs);
}

.input-wrapper--lg .input-icon--left {
  padding-left: var(--spacing-md);
  padding-right: var(--spacing-sm);
}

.input-wrapper--lg .input-icon--right {
  padding-left: var(--spacing-sm);
  padding-right: var(--spacing-md);
}

/* Password Toggle */
.input-password-toggle {
  background: none;
  border: none;
  cursor: pointer;
  color: var(--color-text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  transition: color 0.2s ease-in-out;
  pointer-events: auto;
}

.input-password-toggle:hover {
  color: var(--color-text-primary);
}

/* Input States */
.input-wrapper--focused {
  border-color: var(--color-primary-500);
  box-shadow: 0 0 0 2px var(--color-primary-100);
}

.input-wrapper--error {
  border-color: var(--color-error-500);
}

.input-wrapper--error.input-wrapper--focused {
  box-shadow: 0 0 0 2px var(--color-error-100);
}

.input-wrapper--success {
  border-color: var(--color-success-500);
}

.input-wrapper--success.input-wrapper--focused {
  box-shadow: 0 0 0 2px var(--color-success-100);
}

.input-wrapper--warning {
  border-color: var(--color-warning-500);
}

.input-wrapper--warning.input-wrapper--focused {
  box-shadow: 0 0 0 2px var(--color-warning-100);
}

.input-wrapper--disabled {
  background: var(--color-background-disabled);
  border-color: var(--color-border-light);
  cursor: not-allowed;
}

/* Message Styles */
.input-message {
  font-size: var(--font-size-sm);
  margin-top: var(--spacing-xs);
}

.input-message--default {
  color: var(--color-text-secondary);
}

.input-message--error {
  color: var(--color-error-500);
}

.input-message--success {
  color: var(--color-success-500);
}

.input-message--warning {
  color: var(--color-warning-500);
}

/* GLASSMORPHISM THEME */
[data-theme="glassmorphism"] .input-wrapper {
  backdrop-filter: var(--backdrop-blur-sm);
  border-color: rgba(255, 255, 255, 0.2);
}

[data-theme="glassmorphism"] .input-wrapper--default {
  background: rgba(255, 255, 255, 0.1);
}

[data-theme="glassmorphism"] .input-wrapper--filled {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.25);
}

[data-theme="glassmorphism"] .input-wrapper--outlined {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.3);
  border-width: 2px;
}

[data-theme="glassmorphism"] .input-wrapper--focused {
  border-color: rgba(14, 165, 233, 0.6);
  box-shadow: 0 0 0 2px rgba(14, 165, 233, 0.2);
}

[data-theme="glassmorphism"] .input-wrapper--error {
  border-color: rgba(239, 68, 68, 0.6);
}

[data-theme="glassmorphism"] .input-wrapper--error.input-wrapper--focused {
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
}

[data-theme="glassmorphism"] .input-wrapper--success {
  border-color: rgba(34, 197, 94, 0.6);
}

[data-theme="glassmorphism"] .input-wrapper--success.input-wrapper--focused {
  box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.2);
}

[data-theme="glassmorphism"] .input-wrapper--warning {
  border-color: rgba(245, 158, 11, 0.6);
}

[data-theme="glassmorphism"] .input-wrapper--warning.input-wrapper--focused {
  box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2);
}

/* MINIMAL THEME */
[data-theme="minimal"] .input-wrapper {
  border-color: var(--color-border-light);
}

[data-theme="minimal"] .input-wrapper--default {
  background: var(--color-background-primary);
}

[data-theme="minimal"] .input-wrapper--filled {
  background: var(--color-background-secondary);
  border-color: var(--color-border-light);
}

[data-theme="minimal"] .input-wrapper--outlined {
  background: var(--color-background-primary);
  border-color: var(--color-border-medium);
  border-width: 2px;
}

[data-theme="minimal"] .input-wrapper--focused {
  border-color: var(--color-primary-500);
  box-shadow: 0 0 0 2px var(--color-primary-100);
}

/* NEUMORPHISM THEME */
[data-theme="neumorphism"] .input-wrapper {
  border: none;
  box-shadow: var(--shadow-inner);
}

[data-theme="neumorphism"] .input-wrapper--default {
  background: var(--color-background-primary);
}

[data-theme="neumorphism"] .input-wrapper--filled {
  background: var(--color-background-secondary);
}

[data-theme="neumorphism"] .input-wrapper--outlined {
  background: var(--color-background-primary);
  box-shadow: var(--shadow-md);
}

[data-theme="neumorphism"] .input-wrapper--focused {
  box-shadow: var(--shadow-inner), 0 0 0 2px var(--color-primary-300);
}

[data-theme="neumorphism"] .input-wrapper--error {
  box-shadow: var(--shadow-inner), 0 0 0 1px var(--color-error-500);
}

[data-theme="neumorphism"] .input-wrapper--error.input-wrapper--focused {
  box-shadow: var(--shadow-inner), 0 0 0 2px var(--color-error-300);
}

[data-theme="neumorphism"] .input-wrapper--success {
  box-shadow: var(--shadow-inner), 0 0 0 1px var(--color-success-500);
}

[data-theme="neumorphism"] .input-wrapper--success.input-wrapper--focused {
  box-shadow: var(--shadow-inner), 0 0 0 2px var(--color-success-300);
}

[data-theme="neumorphism"] .input-wrapper--warning {
  box-shadow: var(--shadow-inner), 0 0 0 1px var(--color-warning-500);
}

[data-theme="neumorphism"] .input-wrapper--warning.input-wrapper--focused {
  box-shadow: var(--shadow-inner), 0 0 0 2px var(--color-warning-300);
}

/* BRUTALIST THEME */
[data-theme="brutalist"] .input-wrapper {
  border-radius: var(--border-radius-none);
  border: 3px solid var(--color-neutral-900);
  box-shadow: var(--shadow-sm);
}

[data-theme="brutalist"] .input-wrapper--default {
  background: var(--color-background-primary);
}

[data-theme="brutalist"] .input-wrapper--filled {
  background: var(--color-background-secondary);
  border-color: var(--color-neutral-900);
}

[data-theme="brutalist"] .input-wrapper--outlined {
  background: var(--color-background-primary);
  border-width: 4px;
  border-color: var(--color-neutral-900);
}

[data-theme="brutalist"] .input-wrapper--focused {
  border-color: var(--color-primary-500);
  box-shadow: var(--shadow-md);
  transform: translate(-2px, -2px);
}

[data-theme="brutalist"] .input-wrapper--error {
  border-color: var(--color-error-500);
}

[data-theme="brutalist"] .input-wrapper--error.input-wrapper--focused {
  border-color: var(--color-error-500);
  box-shadow: var(--shadow-md);
}

[data-theme="brutalist"] .input-wrapper--success {
  border-color: var(--color-success-500);
}

[data-theme="brutalist"] .input-wrapper--success.input-wrapper--focused {
  border-color: var(--color-success-500);
  box-shadow: var(--shadow-md);
}

[data-theme="brutalist"] .input-wrapper--warning {
  border-color: var(--color-warning-500);
}

[data-theme="brutalist"] .input-wrapper--warning.input-wrapper--focused {
  border-color: var(--color-warning-500);
  box-shadow: var(--shadow-md);
}

[data-theme="brutalist"] .input-wrapper--sm {
  border-radius: var(--border-radius-none);
}

[data-theme="brutalist"] .input-wrapper--md {
  border-radius: var(--border-radius-none);
}

[data-theme="brutalist"] .input-wrapper--lg {
  border-radius: var(--border-radius-none);
}