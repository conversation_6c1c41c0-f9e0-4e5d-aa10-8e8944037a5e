.input-wrapper {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.input-full-width {
  width: 100%;
}

.input-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-color, #374151);
  margin-bottom: 0.25rem;
}

.input-required {
  color: var(--error-color, #ef4444);
  margin-left: 0.25rem;
}

.input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.input {
  width: 100%;
  border-radius: var(--border-radius-md, 0.375rem);
  border: 1px solid var(--border-color, #d1d5db);
  background-color: var(--input-bg, white);
  color: var(--text-color, #374151);
  transition: all 0.2s ease;
  outline: none;
  padding: 0 0.75rem;
}

.input:focus {
  border-color: var(--primary-color, #3b82f6);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.input-has-left-icon {
  padding-left: 2.5rem;
}

.input-has-right-icon {
  padding-right: 2.5rem;
}

.input-icon {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-color-secondary, #6b7280);
  pointer-events: none;
}

.input-icon-left {
  left: 0.75rem;
}

.input-icon-right {
  right: 0.75rem;
}

.input-message {
  font-size: 0.75rem;
  color: var(--text-color-secondary, #6b7280);
  margin-top: 0.25rem;
}

.input-message-error {
  color: var(--error-color, #ef4444);
}

/* Sizes */
.input-size-sm .input {
  height: 2rem;
  font-size: 0.875rem;
}

.input-size-md .input {
  height: 2.5rem;
  font-size: 1rem;
}

.input-size-lg .input {
  height: 3rem;
  font-size: 1.125rem;
}

/* States */
.input-error .input {
  border-color: var(--error-color, #ef4444);
}

.input-error .input:focus {
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.input-disabled .input {
  background-color: var(--disabled-bg, #f3f4f6);
  color: var(--disabled-color, #9ca3af);
  cursor: not-allowed;
}

.input-disabled .input-label {
  color: var(--disabled-color, #9ca3af);
}

/* Animations */
.input-focused .input {
  transform: translateY(-1px);
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .input-label {
    color: var(--text-color-dark, #e5e7eb);
  }
  
  .input {
    background-color: var(--input-bg-dark, #1f2937);
    border-color: var(--border-color-dark, #4b5563);
    color: var(--text-color-dark, #e5e7eb);
  }
  
  .input-icon {
    color: var(--text-color-secondary-dark, #9ca3af);
  }
  
  .input-message {
    color: var(--text-color-secondary-dark, #9ca3af);
  }
  
  .input-disabled .input {
    background-color: var(--disabled-bg-dark, #111827);
    color: var(--disabled-color-dark, #6b7280);
  }
  
  .input-disabled .input-label {
    color: var(--disabled-color-dark, #6b7280);
  }
}