import type { Meta, StoryObj } from '@storybook/react';
import { Switch } from './Switch';

const meta = {
  title: 'Components/Switch',
  component: Switch,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A toggle switch component for boolean inputs.'
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    size: {
      control: 'select',
      options: ['sm', 'md', 'lg'],
      description: 'The size of the switch'
    },
    disabled: {
      control: 'boolean',
      description: 'Whether the switch is disabled'
    },
    checked: {
      control: 'boolean',
      description: 'Whether the switch is checked'
    }
  },
} satisfies Meta<typeof Switch>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Basic: Story = {
  args: {
    label: 'Enable notifications',
    size: 'md',
  },
};

export const WithHelperText: Story = {
  args: {
    label: 'Dark mode',
    helperText: 'Switch between light and dark theme',
    size: 'md',
  },
};

export const WithError: Story = {
  args: {
    label: 'Accept terms',
    error: 'You must accept the terms to continue',
    size: 'md',
  },
};

export const Sizes: Story = {
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
      <Switch label="Small switch" size="sm" />
      <Switch label="Medium switch" size="md" />
      <Switch label="Large switch" size="lg" />
    </div>
  ),
};

export const Disabled: Story = {
  args: {
    label: 'Disabled switch',
    disabled: true,
    size: 'md',
  },
};

export const Checked: Story = {
  args: {
    label: 'Checked switch',
    checked: true,
    size: 'md',
  },
};