.switch-wrapper {
  display: inline-flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.switch-container {
  display: flex;
  align-items: center;
  position: relative;
}

.switch-input {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.switch-track {
  position: relative;
  display: inline-block;
  background-color: var(--color-border);
  border-radius: var(--radius-full);
  transition: background-color 0.2s ease;
  cursor: pointer;
}

.switch-thumb {
  position: absolute;
  top: 2px;
  left: 2px;
  background-color: white;
  border-radius: var(--radius-full);
  transition: transform 0.2s ease;
  box-shadow: var(--shadow-sm);
}

.switch-input:checked ~ .switch-track {
  background-color: var(--color-primary);
}

.switch-input:focus-visible ~ .switch-track {
  box-shadow: 0 0 0 3px var(--color-focus);
}

.switch-label {
  margin-left: var(--spacing-sm);
  font-size: var(--typography-fontSize-sm);
  color: var(--color-text);
  cursor: pointer;
}

.switch-message {
  font-size: var(--typography-fontSize-xs);
  color: var(--color-text-secondary);
  margin-top: var(--spacing-xs);
}

.switch-message-error {
  color: var(--color-danger);
}

/* Sizes */
.switch-size-sm .switch-track {
  width: 1.75rem;
  height: 1rem;
}

.switch-size-sm .switch-thumb {
  width: 0.75rem;
  height: 0.75rem;
}

.switch-size-sm .switch-input:checked ~ .switch-track .switch-thumb {
  transform: translateX(0.75rem);
}

.switch-size-sm .switch-label {
  font-size: var(--typography-fontSize-xs);
}

.switch-size-md .switch-track {
  width: 2.25rem;
  height: 1.25rem;
}

.switch-size-md .switch-thumb {
  width: 1rem;
  height: 1rem;
}

.switch-size-md .switch-input:checked ~ .switch-track .switch-thumb {
  transform: translateX(1rem);
}

.switch-size-md .switch-label {
  font-size: var(--typography-fontSize-sm);
}

.switch-size-lg .switch-track {
  width: 2.75rem;
  height: 1.5rem;
}

.switch-size-lg .switch-thumb {
  width: 1.25rem;
  height: 1.25rem;
}

.switch-size-lg .switch-input:checked ~ .switch-track .switch-thumb {
  transform: translateX(1.25rem);
}

.switch-size-lg .switch-label {
  font-size: var(--typography-fontSize-md);
}

/* States */
.switch-error .switch-track {
  box-shadow: 0 0 0 1px var(--color-danger);
}

.switch-error .switch-input:focus-visible ~ .switch-track {
  box-shadow: 0 0 0 1px var(--color-danger), 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.switch-disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.switch-disabled .switch-track,
.switch-disabled .switch-label {
  cursor: not-allowed;
}

/* Animations */
.switch-input:active:not(:disabled) ~ .switch-track .switch-thumb {
  width: 1.25rem; /* Slightly wider when pressed */
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .switch-track {
    background-color: var(--switch-bg-off-dark, #4b5563);
  }
  
  .switch-thumb {
    background-color: var(--switch-thumb-dark, #e5e7eb);
  }
  
  .switch-label {
    color: var(--text-color-dark, #e5e7eb);
  }
  
  .switch-message {
    color: var(--text-color-secondary-dark, #9ca3af);
  }
}

