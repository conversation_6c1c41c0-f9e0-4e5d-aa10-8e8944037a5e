{"name": "vite-plugin-babel", "version": "1.3.1", "description": "Runs Babel in Vite during all commands", "main": "dist/index.cjs", "types": "dist/index.d.ts", "module": "dist/index.mjs", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "./*": "./dist/*"}, "files": ["/dist"], "scripts": {"clean": "rm -rf dist", "dev": "rollup -c rollup.config.ts --watch --configPlugin typescript", "build": "rollup -c rollup.config.ts --configPlugin typescript", "prepare": "npm run clean && npm run build"}, "repository": {"type": "git", "url": "git+https://github.com/owlsdepartment/vite-plugin-babel.git"}, "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/owlsdepartment/vite-plugin-babel/issues"}, "homepage": "https://github.com/owlsdepartment/vite-plugin-babel#readme", "keywords": ["vite-plugin", "vite", "babel"], "devDependencies": {"@babel/core": "^7.23.6", "@rollup/plugin-typescript": "^11.1.5", "@types/babel__core": "^7.20.5", "@types/node": "^20.10.4", "rollup": "^4.8.0", "rollup-plugin-dts": "^6.1.0", "rollup-plugin-esbuild": "^6.1.0", "tslib": "^2.6.2", "typescript": "^5.3.3", "vite": "^6.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0", "vite": "^2.7.0 || ^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0"}}