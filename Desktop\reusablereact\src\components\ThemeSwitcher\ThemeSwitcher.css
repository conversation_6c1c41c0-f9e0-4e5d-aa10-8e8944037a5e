.theme-switcher {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 1.5rem;
  border-radius: var(--radius-lg);
  background-color: var(--color-surface);
  box-shadow: var(--shadow-md);
}

.theme-selector h3 {
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: var(--typography-fontSize-lg);
  font-weight: var(--typography-fontWeight-semibold);
}

.theme-options {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.theme-option {
  padding: 0.5rem 1rem;
  border-radius: var(--radius-md);
  background-color: var(--color-background);
  border: 1px solid var(--color-border);
  color: var(--color-text);
  font-size: var(--typography-fontSize-sm);
  cursor: pointer;
  transition: all 0.2s ease;
}

.theme-option:hover {
  background-color: var(--color-primary);
  color: white;
}

.theme-option.active {
  background-color: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
}

.mode-toggle {
  margin-top: 0.5rem;
}