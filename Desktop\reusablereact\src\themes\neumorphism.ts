import type { Theme } from './types';

export const neumorphismTheme: Theme = {
  name: 'neumorphism',
  colors: {
    primary: {
      50: '#f0f4f8',
      100: '#d9e2ec',
      200: '#bcccdc',
      300: '#9fb3c8',
      400: '#829ab1',
      500: '#627d98',
      600: '#486581',
      700: '#334e68',
      800: '#243b53',
      900: '#102a43',
    },
    secondary: {
      50: '#f7f4f3',
      100: '#e6ddd7',
      200: '#d3c4b6',
      300: '#c0a995',
      400: '#a18072',
      500: '#8b5a52',
      600: '#73453c',
      700: '#5c3317',
      800: '#3c1810',
      900: '#2d0b07',
    },
    neutral: {
      50: '#f7fafc',
      100: '#edf2f7',
      200: '#e2e8f0',
      300: '#cbd5e0',
      400: '#a0aec0',
      500: '#718096',
      600: '#4a5568',
      700: '#2d3748',
      800: '#1a202c',
      900: '#171923',
    },
    success: '#48bb78',
    warning: '#ed8936',
    error: '#f56565',
    info: '#4299e1',
    background: {
      primary: '#e0e5ec',
      secondary: '#d1d9e0',
      tertiary: '#c2cdd4',
    },
    text: {
      primary: '#2d3748',
      secondary: '#4a5568',
      tertiary: '#718096',
      inverse: '#ffffff',
    },
    border: {
      light: '#cbd5e0',
      medium: '#a0aec0',
      heavy: '#718096',
    },
  },
  spacing: {
    xs: '0.25rem',
    sm: '0.5rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '2rem',
    '2xl': '3rem',
    '3xl': '4rem',
    '4xl': '6rem',
  },
  typography: {
    fontFamily: {
      sans: 'Poppins, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      serif: 'Playfair Display, Georgia, Cambria, serif',
      mono: 'Fira Code, Menlo, Monaco, Consolas, monospace',
    },
    fontSize: {
      xs: '0.75rem',
      sm: '0.875rem',
      base: '1rem',
      lg: '1.125rem',
      xl: '1.25rem',
      '2xl': '1.5rem',
      '3xl': '1.875rem',
      '4xl': '2.25rem',
      '5xl': '3rem',
    },
    fontWeight: {
      light: 300,
      normal: 400,
      medium: 500,
      semibold: 600,
      bold: 700,
    },
    lineHeight: {
      tight: 1.25,
      normal: 1.5,
      relaxed: 1.75,
    },
  },
  shadows: {
    sm: '2px 2px 4px #c8d0d8, -2px -2px 4px #f8ffff',
    md: '4px 4px 8px #c8d0d8, -4px -4px 8px #f8ffff',
    lg: '6px 6px 12px #c8d0d8, -6px -6px 12px #f8ffff',
    xl: '8px 8px 16px #c8d0d8, -8px -8px 16px #f8ffff',
    '2xl': '12px 12px 24px #c8d0d8, -12px -12px 24px #f8ffff',
    inner: 'inset 2px 2px 4px #c8d0d8, inset -2px -2px 4px #f8ffff',
    none: 'none',
  },
  borderRadius: {
    none: '0',
    sm: '0.25rem',
    md: '0.5rem',
    lg: '0.75rem',
    xl: '1rem',
    '2xl': '1.5rem',
    full: '9999px',
  },
  effects: {
    blur: {
      sm: 'blur(2px)',
      md: 'blur(4px)',
      lg: 'blur(8px)',
      xl: 'blur(16px)',
    },
    backdrop: {
      blur: {
        sm: 'backdrop-blur(2px)',
        md: 'backdrop-blur(4px)',
        lg: 'backdrop-blur(8px)',
        xl: 'backdrop-blur(16px)',
      },
    },
    opacity: {
      10: '0.1',
      20: '0.2',
      30: '0.3',
      40: '0.4',
      50: '0.5',
      60: '0.6',
      70: '0.7',
      80: '0.8',
      90: '0.9',
    },
  },
};