import React, { useState } from 'react';
import './Input.css';

export interface InputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'> {
  /**
   * Input label
   */
  label?: string;
  /**
   * Helper text to display below the input
   */
  helperText?: string;
  /**
   * Error message to display
   */
  error?: string;
  /**
   * Input size
   */
  size?: 'sm' | 'md' | 'lg';
  /**
   * Left icon
   */
  leftIcon?: React.ReactNode;
  /**
   * Right icon
   */
  rightIcon?: React.ReactNode;
  /**
   * Whether the input is full width
   */
  fullWidth?: boolean;
}

export const Input = React.forwardRef<HTMLInputElement, InputProps>(
  (
    {
      className = '',
      label,
      helperText,
      error,
      size = 'md',
      leftIcon,
      rightIcon,
      fullWidth = false,
      id,
      required,
      disabled,
      ...props
    },
    ref
  ) => {
    const [focused, setFocused] = useState(false);
    const inputId = id || `input-${Math.random().toString(36).substring(2, 9)}`;
    
    const baseClass = 'input-wrapper';
    const sizeClass = `input-size-${size}`;
    const focusedClass = focused ? 'input-focused' : '';
    const errorClass = error ? 'input-error' : '';
    const disabledClass = disabled ? 'input-disabled' : '';
    const fullWidthClass = fullWidth ? 'input-full-width' : '';
    
    const wrapperClasses = [
      baseClass,
      sizeClass,
      focusedClass,
      errorClass,
      disabledClass,
      fullWidthClass,
      className
    ].filter(Boolean).join(' ');

    return (
      <div className={wrapperClasses}>
        {label && (
          <label 
            htmlFor={inputId}
            className="input-label"
          >
            {label}
            {required && <span className="input-required">*</span>}
          </label>
        )}
        
        <div className="input-container">
          {leftIcon && <div className="input-icon input-icon-left">{leftIcon}</div>}
          
          <input
            ref={ref}
            id={inputId}
            className={`input ${leftIcon ? 'input-has-left-icon' : ''} ${rightIcon ? 'input-has-right-icon' : ''}`}
            aria-invalid={!!error}
            aria-describedby={error ? `${inputId}-error` : helperText ? `${inputId}-helper` : undefined}
            disabled={disabled}
            required={required}
            onFocus={(e) => {
              setFocused(true);
              props.onFocus?.(e);
            }}
            onBlur={(e) => {
              setFocused(false);
              props.onBlur?.(e);
            }}
            {...props}
          />
          
          {rightIcon && <div className="input-icon input-icon-right">{rightIcon}</div>}
        </div>
        
        {(helperText || error) && (
          <div 
            className={`input-message ${error ? 'input-message-error' : ''}`}
            id={error ? `${inputId}-error` : `${inputId}-helper`}
          >
            {error || helperText}
          </div>
        )}
      </div>
    );
  }
);

Input.displayName = 'Input';