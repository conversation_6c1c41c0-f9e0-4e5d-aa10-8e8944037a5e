# Modern React Component Library

A modern, accessible, and performant React component library built with React 19 and optimized with React Compiler.

## Features

- 🚀 Optimized with React Compiler for maximum performance
- ♿ Fully accessible components with ARIA support
- 🌙 Dark mode support out of the box
- 🌲 Tree-shakable - only import what you need
- 📱 Responsive design for all screen sizes
- 🧪 Thoroughly tested components

## Installation

```bash
npm install your-component-library
# or
yarn add your-component-library
```

## Usage

```jsx
import { Button, Card } from 'your-component-library';

function App() {
  return (
    <div>
      <Card title="Welcome">
        <p>This is a simple example using our components.</p>
        <Button variant="primary">Get Started</Button>
      </Card>
    </div>
  );
}
```

## Available Components

- **Button**: Versatile button component with multiple variants and sizes
- **Card**: Flexible container for grouping related content
- **Input**: Form input with validation support
- **More coming soon!**

## Documentation

For full documentation and examples, visit our [Storybook](https://your-storybook-url.com).

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

MIT © [Your Name]

