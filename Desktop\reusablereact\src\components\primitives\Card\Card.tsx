import React, { forwardRef } from 'react';
import { useTheme } from '../../../themes';
import './Card.css';

export interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  /** Card variant */
  variant?: 'default' | 'elevated' | 'outlined' | 'filled';
  /** Card padding size */
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  /** Whether the card is interactive (hoverable) */
  interactive?: boolean;
  /** Whether the card is selected */
  selected?: boolean;
  /** Card children */
  children?: React.ReactNode;
}

export const Card = forwardRef<HTMLDivElement, CardProps>((
  {
    variant = 'default',
    padding = 'md',
    interactive = false,
    selected = false,
    children,
    className = '',
    ...props
  },
  ref
) => {
  const { themeName } = useTheme();

  const baseClasses = [
    'card',
    `card--${variant}`,
    `card--padding-${padding}`,
    `card--${themeName}`,
    interactive && 'card--interactive',
    selected && 'card--selected',
    className
  ].filter(Boolean).join(' ');

  return (
    <div
      ref={ref}
      className={baseClasses}
      {...props}
    >
      {children}
    </div>
  );
});

Card.displayName = 'Card';

// Card sub-components
export interface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {
  children?: React.ReactNode;
}

export const CardHeader = forwardRef<HTMLDivElement, CardHeaderProps>((
  { children, className = '', ...props },
  ref
) => {
  return (
    <div
      ref={ref}
      className={`card__header ${className}`}
      {...props}
    >
      {children}
    </div>
  );
});

CardHeader.displayName = 'CardHeader';

export interface CardBodyProps extends React.HTMLAttributes<HTMLDivElement> {
  children?: React.ReactNode;
}

export const CardBody = forwardRef<HTMLDivElement, CardBodyProps>((
  { children, className = '', ...props },
  ref
) => {
  return (
    <div
      ref={ref}
      className={`card__body ${className}`}
      {...props}
    >
      {children}
    </div>
  );
});

CardBody.displayName = 'CardBody';

export interface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {
  children?: React.ReactNode;
}

export const CardFooter = forwardRef<HTMLDivElement, CardFooterProps>((
  { children, className = '', ...props },
  ref
) => {
  return (
    <div
      ref={ref}
      className={`card__footer ${className}`}
      {...props}
    >
      {children}
    </div>
  );
});

CardFooter.displayName = 'CardFooter';