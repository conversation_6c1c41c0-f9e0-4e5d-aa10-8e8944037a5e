import type { <PERSON>a, StoryObj } from '@storybook/react';
import { <PERSON>, CardHeader, CardBody, CardFooter } from './Card';
import { Button } from '../Button/Button';

const meta = {
  title: 'Components/Card',
  component: Card,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A flexible card component for grouping related content.'
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: 'select',
      options: ['elevated', 'outlined', 'filled'],
      description: 'The visual style of the card'
    },
    padding: {
      control: 'select',
      options: ['none', 'sm', 'md', 'lg'],
      description: 'The padding size of the card'
    },
    interactive: {
      control: 'boolean',
      description: 'Whether the card has hover effects'
    }
  },
} satisfies Meta<typeof Card>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Basic: Story = {
  args: {
    variant: 'elevated',
    padding: 'md',
    children: 'This is a basic card with some content.',
    style: { maxWidth: '400px' }
  },
};

export const WithSections: Story = {
  render: (args) => (
    <Card {...args} style={{ maxWidth: '400px' }}>
      <CardHeader>Card Header</CardHeader>
      <CardBody>
        <p>This is the main content area of the card. You can put any content here.</p>
      </CardBody>
      <CardFooter>
        <Button variant="outline" size="sm">Cancel</Button>
        <Button variant="primary" size="sm">Save</Button>
      </CardFooter>
    </Card>
  ),
  args: {
    variant: 'elevated',
    padding: 'none',
  },
};

export const Interactive: Story = {
  args: {
    variant: 'elevated',
    padding: 'md',
    interactive: true,
    children: 'This card has hover effects. Try hovering over it!',
    style: { maxWidth: '400px' }
  },
};

export const Outlined: Story = {
  args: {
    variant: 'outlined',
    padding: 'md',
    children: 'This is an outlined card with a border instead of elevation.',
    style: { maxWidth: '400px' }
  },
};

export const Filled: Story = {
  args: {
    variant: 'filled',
    padding: 'md',
    children: 'This is a filled card with a background color.',
    style: { maxWidth: '400px' }
  },
};