import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { Input } from './Input';
import { ThemeProvider } from '../../../themes';

const meta: Meta<typeof Input> = {
  title: 'Primitives/Input',
  component: Input,
  parameters: {
    layout: 'padded',
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: { type: 'select' },
      options: ['default', 'filled', 'outlined'],
    },
    size: {
      control: { type: 'select' },
      options: ['sm', 'md', 'lg'],
    },
    state: {
      control: { type: 'select' },
      options: ['default', 'error', 'success', 'warning'],
    },
    type: {
      control: { type: 'select' },
      options: ['text', 'email', 'password', 'number', 'tel', 'url', 'search'],
    },
    disabled: {
      control: { type: 'boolean' },
    },
    required: {
      control: { type: 'boolean' },
    },
    fullWidth: {
      control: { type: 'boolean' },
    },
    showPasswordToggle: {
      control: { type: 'boolean' },
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Basic Input Stories
export const Default: Story = {
  args: {
    placeholder: 'Enter text...',
  },
};

export const WithLabel: Story = {
  args: {
    label: 'Email Address',
    placeholder: 'Enter your email',
    type: 'email',
  },
};

export const WithHelperText: Story = {
  args: {
    label: 'Username',
    placeholder: 'Enter username',
    helperText: 'Username must be at least 3 characters long',
  },
};

export const Required: Story = {
  args: {
    label: 'Full Name',
    placeholder: 'Enter your full name',
    required: true,
    helperText: 'This field is required',
  },
};

// Size Variations
export const Small: Story = {
  args: {
    size: 'sm',
    label: 'Small Input',
    placeholder: 'Small size input',
  },
};

export const Medium: Story = {
  args: {
    size: 'md',
    label: 'Medium Input',
    placeholder: 'Medium size input',
  },
};

export const Large: Story = {
  args: {
    size: 'lg',
    label: 'Large Input',
    placeholder: 'Large size input',
  },
};

// Variant Styles
export const Filled: Story = {
  args: {
    variant: 'filled',
    label: 'Filled Input',
    placeholder: 'Filled variant',
  },
};

export const Outlined: Story = {
  args: {
    variant: 'outlined',
    label: 'Outlined Input',
    placeholder: 'Outlined variant',
  },
};

// State Variations
export const ErrorState: Story = {
  args: {
    label: 'Email',
    placeholder: 'Enter email',
    value: 'invalid-email',
    errorMessage: 'Please enter a valid email address',
  },
};

export const SuccessState: Story = {
  args: {
    label: 'Email',
    placeholder: 'Enter email',
    value: '<EMAIL>',
    successMessage: 'Email is valid',
  },
};

export const WarningState: Story = {
  args: {
    label: 'Password',
    type: 'password',
    placeholder: 'Enter password',
    value: '123',
    warningMessage: 'Password is too short',
  },
};

// Special Input Types
export const PasswordInput: Story = {
  args: {
    type: 'password',
    label: 'Password',
    placeholder: 'Enter your password',
    showPasswordToggle: true,
  },
};

export const SearchInput: Story = {
  args: {
    type: 'search',
    placeholder: 'Search...',
    leftIcon: (
      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
        <circle cx="11" cy="11" r="8"/>
        <path d="m21 21-4.35-4.35"/>
      </svg>
    ),
  },
};

export const NumberInput: Story = {
  args: {
    type: 'number',
    label: 'Age',
    placeholder: 'Enter your age',
    min: 0,
    max: 120,
  },
};

// With Icons
export const WithLeftIcon: Story = {
  args: {
    label: 'Email',
    placeholder: 'Enter email',
    leftIcon: (
      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
        <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
        <polyline points="22,6 12,13 2,6"/>
      </svg>
    ),
  },
};

export const WithRightIcon: Story = {
  args: {
    label: 'Website',
    placeholder: 'Enter website URL',
    rightIcon: (
      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
        <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"/>
        <polyline points="15,3 21,3 21,9"/>
        <line x1="10" y1="14" x2="21" y2="3"/>
      </svg>
    ),
  },
};

export const WithBothIcons: Story = {
  args: {
    label: 'Phone Number',
    placeholder: 'Enter phone number',
    leftIcon: (
      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
        <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
      </svg>
    ),
    rightIcon: (
      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
        <polyline points="20,6 9,17 4,12"/>
      </svg>
    ),
  },
};

// Disabled State
export const Disabled: Story = {
  args: {
    label: 'Disabled Input',
    placeholder: 'This input is disabled',
    disabled: true,
    value: 'Cannot edit this',
  },
};

// Full Width
export const FullWidth: Story = {
  args: {
    label: 'Full Width Input',
    placeholder: 'This input takes full width',
    fullWidth: true,
  },
};

// Form Example
export const FormExample: Story = {
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem', maxWidth: '400px' }}>
      <Input
        label="First Name"
        placeholder="Enter first name"
        required
      />
      <Input
        label="Last Name"
        placeholder="Enter last name"
        required
      />
      <Input
        label="Email"
        type="email"
        placeholder="Enter email address"
        required
        leftIcon={(
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
            <polyline points="22,6 12,13 2,6"/>
          </svg>
        )}
      />
      <Input
        label="Password"
        type="password"
        placeholder="Enter password"
        required
        showPasswordToggle
        helperText="Password must be at least 8 characters"
      />
      <Input
        label="Phone Number"
        type="tel"
        placeholder="Enter phone number"
        leftIcon={(
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
          </svg>
        )}
      />
    </div>
  ),
};

// Theme Showcase
export const ThemeShowcase: Story = {
  render: () => (
    <div style={{ display: 'grid', gap: '2rem' }}>
      {(['glassmorphism', 'minimal', 'neumorphism', 'brutalist'] as const).map((theme) => (
        <ThemeProvider key={theme} initialTheme={theme}>
          <div>
            <h3 style={{ marginBottom: '1rem', textTransform: 'capitalize' }}>{theme} Theme</h3>
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '1rem' }}>
              <Input
                variant="default"
                label="Default Input"
                placeholder="Default variant"
              />
              
              <Input
                variant="filled"
                label="Filled Input"
                placeholder="Filled variant"
              />
              
              <Input
                variant="outlined"
                label="Outlined Input"
                placeholder="Outlined variant"
              />
              
              <Input
                label="With Icon"
                placeholder="Search..."
                leftIcon={(
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <circle cx="11" cy="11" r="8"/>
                    <path d="m21 21-4.35-4.35"/>
                  </svg>
                )}
              />
            </div>
          </div>
        </ThemeProvider>
      ))}
    </div>
  ),
  parameters: {
    layout: 'padded',
  },
};

// State Showcase
export const StateShowcase: Story = {
  render: () => (
    <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '1rem' }}>
      <Input
        label="Default State"
        placeholder="Normal input"
        helperText="This is helper text"
      />
      
      <Input
        label="Error State"
        placeholder="Invalid input"
        value="invalid@"
        errorMessage="Please enter a valid email"
      />
      
      <Input
        label="Success State"
        placeholder="Valid input"
        value="<EMAIL>"
        successMessage="Email is valid"
      />
      
      <Input
        label="Warning State"
        placeholder="Warning input"
        value="weak"
        warningMessage="Password is too weak"
      />
      
      <Input
        label="Disabled State"
        placeholder="Disabled input"
        disabled
        value="Cannot edit"
      />
    </div>
  ),
};