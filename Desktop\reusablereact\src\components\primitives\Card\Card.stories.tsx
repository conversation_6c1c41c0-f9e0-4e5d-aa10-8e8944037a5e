import type { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react';
import { <PERSON>, CardHeader, CardBody, CardFooter } from './Card';
import { Button } from '../Button';
import { ThemeProvider } from '../../../themes';

const meta: Meta<typeof Card> = {
  title: 'Primitives/Card',
  component: Card,
  parameters: {
    layout: 'padded',
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: { type: 'select' },
      options: ['default', 'elevated', 'outlined', 'filled'],
    },
    padding: {
      control: { type: 'select' },
      options: ['none', 'sm', 'md', 'lg', 'xl'],
    },
    interactive: {
      control: { type: 'boolean' },
    },
    selected: {
      control: { type: 'boolean' },
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Basic Card Stories
export const Default: Story = {
  args: {
    children: (
      <>
        <CardHeader>
          <h3 style={{ margin: 0, fontSize: '1.25rem', fontWeight: 600 }}>Card Title</h3>
        </CardHeader>
        <CardBody>
          <p style={{ margin: 0, color: 'var(--color-text-secondary)' }}>
            This is a basic card with some content. Cards are flexible containers that can hold various types of content.
          </p>
        </CardBody>
        <CardFooter>
          <Button variant="outline" size="sm">Cancel</Button>
          <Button variant="primary" size="sm">Save</Button>
        </CardFooter>
      </>
    ),
  },
};

export const Elevated: Story = {
  args: {
    variant: 'elevated',
    children: (
      <>
        <CardHeader>
          <h3 style={{ margin: 0, fontSize: '1.25rem', fontWeight: 600 }}>Elevated Card</h3>
        </CardHeader>
        <CardBody>
          <p style={{ margin: 0, color: 'var(--color-text-secondary)' }}>
            This card has an elevated appearance with enhanced shadows and depth.
          </p>
        </CardBody>
      </>
    ),
  },
};

export const Outlined: Story = {
  args: {
    variant: 'outlined',
    children: (
      <>
        <CardHeader>
          <h3 style={{ margin: 0, fontSize: '1.25rem', fontWeight: 600 }}>Outlined Card</h3>
        </CardHeader>
        <CardBody>
          <p style={{ margin: 0, color: 'var(--color-text-secondary)' }}>
            This card emphasizes its border for a more defined appearance.
          </p>
        </CardBody>
      </>
    ),
  },
};

export const Filled: Story = {
  args: {
    variant: 'filled',
    children: (
      <>
        <CardHeader>
          <h3 style={{ margin: 0, fontSize: '1.25rem', fontWeight: 600 }}>Filled Card</h3>
        </CardHeader>
        <CardBody>
          <p style={{ margin: 0, color: 'var(--color-text-secondary)' }}>
            This card has a filled background for better contrast.
          </p>
        </CardBody>
      </>
    ),
  },
};

export const Interactive: Story = {
  args: {
    interactive: true,
    children: (
      <>
        <CardHeader>
          <h3 style={{ margin: 0, fontSize: '1.25rem', fontWeight: 600 }}>Interactive Card</h3>
        </CardHeader>
        <CardBody>
          <p style={{ margin: 0, color: 'var(--color-text-secondary)' }}>
            This card responds to hover interactions. Try hovering over it!
          </p>
        </CardBody>
      </>
    ),
  },
};

export const Selected: Story = {
  args: {
    selected: true,
    children: (
      <>
        <CardHeader>
          <h3 style={{ margin: 0, fontSize: '1.25rem', fontWeight: 600 }}>Selected Card</h3>
        </CardHeader>
        <CardBody>
          <p style={{ margin: 0, color: 'var(--color-text-secondary)' }}>
            This card is in a selected state with special styling.
          </p>
        </CardBody>
      </>
    ),
  },
};

export const NoPadding: Story = {
  args: {
    padding: 'none',
    children: (
      <div style={{ padding: '1rem' }}>
        <h3 style={{ margin: '0 0 0.5rem 0', fontSize: '1.25rem', fontWeight: 600 }}>No Padding Card</h3>
        <p style={{ margin: 0, color: 'var(--color-text-secondary)' }}>
          This card has no internal padding, allowing for custom spacing.
        </p>
      </div>
    ),
  },
};

export const LargePadding: Story = {
  args: {
    padding: 'xl',
    children: (
      <>
        <CardHeader>
          <h3 style={{ margin: 0, fontSize: '1.25rem', fontWeight: 600 }}>Large Padding</h3>
        </CardHeader>
        <CardBody>
          <p style={{ margin: 0, color: 'var(--color-text-secondary)' }}>
            This card has extra large padding for a more spacious feel.
          </p>
        </CardBody>
      </>
    ),
  },
};

// Simple content cards
export const SimpleCard: Story = {
  args: {
    children: (
      <div>
        <h3 style={{ margin: '0 0 0.5rem 0', fontSize: '1.25rem', fontWeight: 600 }}>Simple Card</h3>
        <p style={{ margin: 0, color: 'var(--color-text-secondary)' }}>
          A simple card without sub-components.
        </p>
      </div>
    ),
  },
};

export const HeaderOnly: Story = {
  args: {
    children: (
      <CardHeader>
        <h3 style={{ margin: 0, fontSize: '1.25rem', fontWeight: 600 }}>Header Only Card</h3>
        <Button variant="ghost" size="sm">Action</Button>
      </CardHeader>
    ),
  },
};

export const BodyOnly: Story = {
  args: {
    children: (
      <CardBody>
        <p style={{ margin: 0, color: 'var(--color-text-secondary)' }}>
          This card only contains body content without header or footer.
        </p>
      </CardBody>
    ),
  },
};

// Theme showcase
export const ThemeShowcase: Story = {
  render: () => (
    <div style={{ display: 'grid', gap: '2rem' }}>
      {(['glassmorphism', 'minimal', 'neumorphism', 'brutalist'] as const).map((theme) => (
        <ThemeProvider key={theme} initialTheme={theme}>
          <div>
            <h3 style={{ marginBottom: '1rem', textTransform: 'capitalize' }}>{theme} Theme</h3>
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '1rem' }}>
              <Card variant="default">
                <CardHeader>
                  <h4 style={{ margin: 0, fontSize: '1.1rem', fontWeight: 600 }}>Default Card</h4>
                </CardHeader>
                <CardBody>
                  <p style={{ margin: 0, color: 'var(--color-text-secondary)', fontSize: '0.9rem' }}>
                    Default variant in {theme} theme.
                  </p>
                </CardBody>
              </Card>
              
              <Card variant="elevated">
                <CardHeader>
                  <h4 style={{ margin: 0, fontSize: '1.1rem', fontWeight: 600 }}>Elevated Card</h4>
                </CardHeader>
                <CardBody>
                  <p style={{ margin: 0, color: 'var(--color-text-secondary)', fontSize: '0.9rem' }}>
                    Elevated variant in {theme} theme.
                  </p>
                </CardBody>
              </Card>
              
              <Card variant="outlined">
                <CardHeader>
                  <h4 style={{ margin: 0, fontSize: '1.1rem', fontWeight: 600 }}>Outlined Card</h4>
                </CardHeader>
                <CardBody>
                  <p style={{ margin: 0, color: 'var(--color-text-secondary)', fontSize: '0.9rem' }}>
                    Outlined variant in {theme} theme.
                  </p>
                </CardBody>
              </Card>
              
              <Card variant="filled">
                <CardHeader>
                  <h4 style={{ margin: 0, fontSize: '1.1rem', fontWeight: 600 }}>Filled Card</h4>
                </CardHeader>
                <CardBody>
                  <p style={{ margin: 0, color: 'var(--color-text-secondary)', fontSize: '0.9rem' }}>
                    Filled variant in {theme} theme.
                  </p>
                </CardBody>
              </Card>
            </div>
          </div>
        </ThemeProvider>
      ))}
    </div>
  ),
  parameters: {
    layout: 'padded',
  },
};

// Interactive showcase
export const InteractiveShowcase: Story = {
  render: () => (
    <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '1rem' }}>
      <Card interactive>
        <CardHeader>
          <h4 style={{ margin: 0, fontSize: '1.1rem', fontWeight: 600 }}>Hover Me</h4>
        </CardHeader>
        <CardBody>
          <p style={{ margin: 0, color: 'var(--color-text-secondary)', fontSize: '0.9rem' }}>
            This card responds to hover interactions.
          </p>
        </CardBody>
      </Card>
      
      <Card interactive selected>
        <CardHeader>
          <h4 style={{ margin: 0, fontSize: '1.1rem', fontWeight: 600 }}>Selected & Interactive</h4>
        </CardHeader>
        <CardBody>
          <p style={{ margin: 0, color: 'var(--color-text-secondary)', fontSize: '0.9rem' }}>
            This card is both selected and interactive.
          </p>
        </CardBody>
      </Card>
      
      <Card interactive variant="elevated">
        <CardHeader>
          <h4 style={{ margin: 0, fontSize: '1.1rem', fontWeight: 600 }}>Elevated & Interactive</h4>
        </CardHeader>
        <CardBody>
          <p style={{ margin: 0, color: 'var(--color-text-secondary)', fontSize: '0.9rem' }}>
            Elevated card with hover effects.
          </p>
        </CardBody>
        <CardFooter>
          <Button variant="primary" size="sm">Action</Button>
        </CardFooter>
      </Card>
    </div>
  ),
};