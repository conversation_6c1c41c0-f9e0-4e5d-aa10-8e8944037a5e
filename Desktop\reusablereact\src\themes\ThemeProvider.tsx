import React, { createContext, useContext, useState, useEffect } from 'react';
import type { ReactNode } from 'react';
import type { Theme, ThemeName } from './types';
import { glassmorphismTheme } from './glassmorphism';
import { minimalTheme } from './minimal';
import { neumorphismTheme } from './neumorphism';
import { brutalistTheme } from './brutalist';

interface ThemeContextType {
  theme: Theme;
  themeName: ThemeName;
  setTheme: (themeName: ThemeName) => void;
  availableThemes: ThemeName[];
  isDarkMode: boolean;
  toggleDarkMode: () => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

const themes: Record<ThemeName, Theme> = {
  glassmorphism: glassmorphismTheme,
  minimal: minimalTheme,
  neumorphism: neumorphismTheme,
  brutalist: brutalistTheme,
};

interface ThemeProviderProps {
  children: ReactNode;
  initialTheme?: ThemeName;
  initialDarkMode?: boolean;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ 
  children, 
  initialTheme = 'glassmorphism',
  initialDarkMode = false
}) => {
  const [themeName, setThemeName] = useState<ThemeName>(initialTheme);
  const [isDarkMode, setIsDarkMode] = useState<boolean>(initialDarkMode);
  
  const theme = themes[themeName];
  const availableThemes: ThemeName[] = Object.keys(themes) as ThemeName[];

  const setTheme = (newThemeName: ThemeName) => {
    setThemeName(newThemeName);
  };

  const toggleDarkMode = () => {
    setIsDarkMode(prev => !prev);
  };

  // Apply CSS variables to document root
  useEffect(() => {
    const root = document.documentElement;
    const colors = isDarkMode ? theme.colors.dark : theme.colors.light;
    
    // Apply color variables
    Object.entries(colors).forEach(([key, value]) => {
      if (typeof value === 'string') {
        root.style.setProperty(`--color-${key}`, value);
      } else if (typeof value === 'object') {
        Object.entries(value).forEach(([subKey, subValue]) => {
          root.style.setProperty(`--color-${key}-${subKey}`, subValue as string);
        });
      }
    });
    
    // Apply spacing variables
    Object.entries(theme.spacing).forEach(([key, value]) => {
      root.style.setProperty(`--spacing-${key}`, value);
    });
    
    // Apply typography variables
    Object.entries(theme.typography).forEach(([key, value]) => {
      if (typeof value === 'string') {
        root.style.setProperty(`--typography-${key}`, value);
      } else if (typeof value === 'object') {
        Object.entries(value).forEach(([subKey, subValue]) => {
          root.style.setProperty(`--typography-${key}-${subKey}`, subValue as string);
        });
      }
    });
    
    // Apply shadow variables
    Object.entries(theme.shadows).forEach(([key, value]) => {
      root.style.setProperty(`--shadow-${key}`, value);
    });
    
    // Apply border radius variables
    Object.entries(theme.borderRadius).forEach(([key, value]) => {
      root.style.setProperty(`--radius-${key}`, value);
    });
    
    // Apply effect variables
    Object.entries(theme.effects.blur).forEach(([key, value]) => {
      root.style.setProperty(`--blur-${key}`, value);
    });
    
    // Set theme and mode attributes on body
    document.body.setAttribute('data-theme', themeName);
    document.body.setAttribute('data-mode', isDarkMode ? 'dark' : 'light');
    
  }, [theme, themeName, isDarkMode]);

  return (
    <ThemeContext.Provider 
      value={{ 
        theme, 
        themeName, 
        setTheme, 
        availableThemes,
        isDarkMode,
        toggleDarkMode
      }}
    >
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

