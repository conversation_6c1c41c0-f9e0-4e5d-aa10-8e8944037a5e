{"name": "babel-plugin-react-compiler", "version": "19.0.0-beta-af1b7da-20250417", "description": "Babel plugin for React Compiler.", "main": "dist/index.js", "license": "MIT", "files": ["dist", "!*.tsbuil<PERSON><PERSON>"], "scripts": {"build": "rimraf dist && tsup", "test": "./scripts/link-react-compiler-runtime.sh && yarn snap:ci", "jest": "yarn build && ts-node node_modules/.bin/jest", "snap": "yarn workspace snap run snap", "snap:build": "yarn workspace snap run build", "snap:ci": "yarn snap:build && yarn snap", "ts:analyze-trace": "scripts/ts-analyze-trace.sh", "lint": "yarn eslint src", "watch": "yarn build --dts --watch"}, "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"@babel/core": "^7.2.0", "@babel/generator": "7.2.0", "@babel/parser": "^7.2.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6", "@babel/preset-react": "^7.18.6", "@babel/preset-typescript": "^7.18.6", "@babel/traverse": "^7.2.0", "@testing-library/react": "^13.4.0", "@tsconfig/node18-strictest": "^1.0.0", "@types/glob": "^8.1.0", "@types/invariant": "^2.2.35", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@typescript-eslint/eslint-plugin": "^8.7.0", "@typescript-eslint/parser": "^8.7.0", "babel-jest": "^29.0.3", "babel-plugin-fbt": "^1.0.0", "babel-plugin-fbt-runtime": "^1.0.0", "eslint": "^8.57.1", "invariant": "^2.2.4", "jest": "^29.0.3", "jest-environment-jsdom": "^29.0.3", "pretty-format": "^24", "react": "0.0.0-experimental-4beb1fd8-20241118", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "zod": "^3.22.4", "zod-validation-error": "^2.1.0"}, "resolutions": {"./**/@babel/parser": "7.7.4", "./**/@babel/plugin-syntax-flow": "7.7.4", "./**/@babel/types": "7.7.4", "@babel/core": "7.2.0", "@babel/generator": "7.2.0", "@babel/traverse": "7.7.4"}, "repository": {"type": "git", "url": "git+https://github.com/facebook/react.git", "directory": "compiler/packages/babel-plugin-react-compiler"}}