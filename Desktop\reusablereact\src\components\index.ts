// Component library entry point

// Export all components from here
export { Button } from './primitives/Button';
export { <PERSON>, CardHeader, CardBody, CardFooter } from './primitives/Card';
export { Input } from './primitives/Input';
export { Modal, ModalHeader, ModalBody, ModalFooter } from './primitives/Modal';
export { Switch } from './Switch';

// Export types
export type { ButtonProps } from './primitives/Button';
export type { CardProps, CardHeaderProps, CardBodyProps, CardFooterProps } from './primitives/Card';
export type { InputProps } from './primitives/Input';
export type { ModalProps, ModalHeaderProps, ModalBodyProps, ModalFooterProps } from './primitives/Modal';
export type { SwitchProps } from './Switch';



