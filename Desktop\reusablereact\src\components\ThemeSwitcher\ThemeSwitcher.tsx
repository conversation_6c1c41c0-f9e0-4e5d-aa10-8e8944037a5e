import React from 'react';
import { useTheme } from '../../themes';
import { Switch } from '../Switch';
import './ThemeSwitcher.css';

export const ThemeSwitcher: React.FC = () => {
  const { themeName, setTheme, availableThemes, isDarkMode, toggleDarkMode } = useTheme();

  return (
    <div className="theme-switcher">
      <div className="theme-selector">
        <h3>Select Theme</h3>
        <div className="theme-options">
          {availableThemes.map((theme) => (
            <button
              key={theme}
              className={`theme-option ${themeName === theme ? 'active' : ''}`}
              onClick={() => setTheme(theme)}
              aria-pressed={themeName === theme}
            >
              {theme.charAt(0).toUpperCase() + theme.slice(1)}
            </button>
          ))}
        </div>
      </div>
      
      <div className="mode-toggle">
        <Switch
          label="Dark Mode"
          checked={isDarkMode}
          onChange={toggleDarkMode}
        />
      </div>
    </div>
  );
};