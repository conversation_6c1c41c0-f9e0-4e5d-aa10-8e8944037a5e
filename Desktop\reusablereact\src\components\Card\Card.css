.card {
  border-radius: var(--border-radius-md, 0.5rem);
  overflow: hidden;
  transition: all 0.2s ease-in-out;
  width: 100%;
}

/* Variants */
.card-elevated {
  background-color: var(--card-bg, white);
  box-shadow: var(--shadow-md, 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06));
}

.card-outlined {
  background-color: var(--card-bg, white);
  border: 1px solid var(--border-color, rgba(0, 0, 0, 0.1));
}

.card-filled {
  background-color: var(--card-bg-filled, #f9fafb);
}

/* Padding */
.card-padding-sm {
  padding: 0.75rem;
}

.card-padding-md {
  padding: 1.25rem;
}

.card-padding-lg {
  padding: 2rem;
}

/* Interactive */
.card-interactive {
  cursor: pointer;
}

.card-interactive:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg, 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05));
}

.card-interactive:active {
  transform: translateY(0);
}

/* Card sections */
.card-header {
  padding: 1.25rem;
  border-bottom: 1px solid var(--border-color, rgba(0, 0, 0, 0.1));
  font-weight: 600;
}

.card-body {
  padding: 1.25rem;
}

.card-footer {
  padding: 1.25rem;
  border-top: 1px solid var(--border-color, rgba(0, 0, 0, 0.1));
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .card-elevated, .card-outlined {
    background-color: var(--card-bg-dark, #1f2937);
    color: var(--text-color-dark, #e5e7eb);
  }
  
  .card-outlined {
    border-color: var(--border-color-dark, rgba(255, 255, 255, 0.1));
  }
  
  .card-filled {
    background-color: var(--card-bg-filled-dark, #111827);
    color: var(--text-color-dark, #e5e7eb);
  }
  
  .card-header {
    border-bottom-color: var(--border-color-dark, rgba(255, 255, 255, 0.1));
  }
  
  .card-footer {
    border-top-color: var(--border-color-dark, rgba(255, 255, 255, 0.1));
  }
}