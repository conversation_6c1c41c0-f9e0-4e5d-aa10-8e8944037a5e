
import { ThemeProvider } from './themes';
import { SwitchExample } from './examples/SwitchExample';
import { ThemeExample } from './examples/ThemeExample';
import './App.css';

function App() {
  return (
    <ThemeProvider initialTheme="glassmorphism" initialDarkMode={false}>
      <div className="app">
        <div className="app-container">
          <header className="app-header">
            <h1>Reusable React Components</h1>
            <p className="subtitle">
              A modern, accessible component library with beautiful themes and smooth interactions
            </p>
          </header>
          <main className="app-main">
            <ThemeExample />
            <SwitchExample />
          </main>
        </div>
      </div>
    </ThemeProvider>
  );
}

export default App;


