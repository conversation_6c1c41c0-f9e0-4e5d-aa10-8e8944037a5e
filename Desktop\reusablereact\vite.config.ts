import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
// Uncomment when ready to add React Compiler
// import babel from 'vite-plugin-babel'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react(),
    // Uncomment when ready to add React Compiler
    // babel({
    //   filter: /\.[jt]sx?$/,
    //   babelConfig: {
    //     plugins: [
    //       ["babel-plugin-react-compiler", { target: '19' }],
    //     ],
    //   },
    // }),
  ],
})






