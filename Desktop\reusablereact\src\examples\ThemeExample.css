.theme-example {
  display: flex;
  flex-direction: column;
  gap: 3rem;
}

.theme-example h2 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.theme-controls {
  max-width: 600px;
  margin: 0 auto;
}

.theme-preview {
  display: grid;
  gap: 3rem;
}

.theme-status {
  text-align: center;
  padding: 1.5rem;
  background: linear-gradient(135deg,
    rgba(var(--color-primary), 0.1),
    rgba(var(--color-secondary), 0.1)
  );
  border-radius: var(--radius-xl);
  border: 1px solid var(--color-border);
  backdrop-filter: blur(10px);
  margin-bottom: 2rem;
}

.theme-status h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  color: var(--color-text);
}

.color-palette {
  background: var(--color-surface);
  padding: 2rem;
  border-radius: var(--radius-xl);
  border: 1px solid var(--color-border);
  backdrop-filter: blur(20px);
  box-shadow: var(--shadow-lg);
}

.color-palette h4 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--color-text);
}

.color-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 1.5rem;
}

.color-item {
  height: 100px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: flex-end;
  padding: 1rem;
  box-shadow: var(--shadow-md);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
}

.color-item:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.color-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent 0%, rgba(0, 0, 0, 0.1) 100%);
  pointer-events: none;
}

.color-item span {
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 0.5rem 0.75rem;
  border-radius: var(--radius-md);
  font-size: var(--typography-fontSize-sm);
  font-weight: var(--typography-fontWeight-semibold);
  color: #1f2937;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
}

.component-preview {
  background: var(--color-surface);
  padding: 2rem;
  border-radius: var(--radius-xl);
  border: 1px solid var(--color-border);
  backdrop-filter: blur(20px);
  box-shadow: var(--shadow-lg);
}

.component-preview h4 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--color-text);
}

.button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin: 1.5rem 0;
}

.typography-preview {
  background: var(--color-surface);
  padding: 2rem;
  border-radius: var(--radius-xl);
  border: 1px solid var(--color-border);
  backdrop-filter: blur(20px);
  box-shadow: var(--shadow-lg);
}

.typography-preview h4 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--color-text);
}

.typography-samples {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.typography-samples h1,
.typography-samples h2,
.typography-samples h3,
.typography-samples h4 {
  margin: 0;
}

.typography-samples p {
  margin: 0;
  line-height: 1.6;
}

.typography-samples small {
  opacity: 0.8;
}

.typography-samples code {
  background: rgba(var(--color-primary), 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm);
  font-family: var(--typography-fontFamily-mono);
}