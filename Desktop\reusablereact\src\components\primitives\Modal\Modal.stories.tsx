import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { useState } from 'react';
import { Modal, ModalBody, ModalFooter } from './Modal';
import { Button } from '../Button';
import { Input } from '../Input';
import { ThemeProvider } from '../../../themes';

const meta: Meta<typeof Modal> = {
  title: 'Primitives/Modal',
  component: Modal,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    size: {
      control: { type: 'select' },
      options: ['sm', 'md', 'lg', 'xl', 'full'],
    },
    showCloseButton: {
      control: { type: 'boolean' },
    },
    closeOnOverlayClick: {
      control: { type: 'boolean' },
    },
    closeOnEscape: {
      control: { type: 'boolean' },
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Helper component for interactive stories
const ModalDemo: React.FC<{
  modalProps?: Partial<React.ComponentProps<typeof Modal>>;
  buttonText?: string;
  children?: React.ReactNode;
}> = ({ modalProps = {}, buttonText = 'Open Modal', children }) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <>
      <Button onClick={() => setIsOpen(true)}>{buttonText}</Button>
      <Modal
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        {...modalProps}
      >
        {children}
      </Modal>
    </>
  );
};

// Basic Modal Stories
export const Default: Story = {
  render: () => (
    <ModalDemo
      modalProps={{
        title: 'Default Modal',
      }}
    >
      <ModalBody>
        <p>This is a basic modal with default settings. It includes a title, close button, and can be closed by clicking the overlay or pressing the Escape key.</p>
      </ModalBody>
    </ModalDemo>
  ),
};

export const WithFooter: Story = {
  render: () => (
    <ModalDemo
      modalProps={{
        title: 'Modal with Footer',
      }}
    >
      <ModalBody>
        <p>This modal includes a footer with action buttons. The footer is commonly used for confirmation dialogs or forms.</p>
      </ModalBody>
      <ModalFooter>
        <Button variant="outline">Cancel</Button>
        <Button variant="primary">Save Changes</Button>
      </ModalFooter>
    </ModalDemo>
  ),
};

export const NoCloseButton: Story = {
  render: () => (
    <ModalDemo
      modalProps={{
        title: 'No Close Button',
        showCloseButton: false,
      }}
    >
      <ModalBody>
        <p>This modal doesn't show the close button. Users can still close it by clicking the overlay or pressing Escape.</p>
      </ModalBody>
      <ModalFooter>
        <Button variant="primary">Close</Button>
      </ModalFooter>
    </ModalDemo>
  ),
};

export const NoOverlayClose: Story = {
  render: () => (
    <ModalDemo
      modalProps={{
        title: 'No Overlay Close',
        closeOnOverlayClick: false,
      }}
    >
      <ModalBody>
        <p>This modal cannot be closed by clicking the overlay. Users must use the close button or press Escape.</p>
      </ModalBody>
    </ModalDemo>
  ),
};

export const NoEscapeClose: Story = {
  render: () => (
    <ModalDemo
      modalProps={{
        title: 'No Escape Close',
        closeOnEscape: false,
      }}
    >
      <ModalBody>
        <p>This modal cannot be closed by pressing the Escape key. Users must use the close button or click the overlay.</p>
      </ModalBody>
    </ModalDemo>
  ),
};

// Size Variations
export const SmallModal: Story = {
  render: () => (
    <ModalDemo
      modalProps={{
        title: 'Small Modal',
        size: 'sm',
      }}
      buttonText="Open Small Modal"
    >
      <ModalBody>
        <p>This is a small modal, perfect for simple confirmations or alerts.</p>
      </ModalBody>
      <ModalFooter>
        <Button variant="outline">Cancel</Button>
        <Button variant="primary">Confirm</Button>
      </ModalFooter>
    </ModalDemo>
  ),
};

export const LargeModal: Story = {
  render: () => (
    <ModalDemo
      modalProps={{
        title: 'Large Modal',
        size: 'lg',
      }}
      buttonText="Open Large Modal"
    >
      <ModalBody>
        <p>This is a large modal with more space for content. It's suitable for forms, detailed information, or complex interactions.</p>
        <div style={{ marginTop: '1rem' }}>
          <h4>Sample Form</h4>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem', marginTop: '0.5rem' }}>
            <Input label="Name" placeholder="Enter your name" />
            <Input label="Email" type="email" placeholder="Enter your email" />
            <Input label="Message" placeholder="Enter your message" />
          </div>
        </div>
      </ModalBody>
      <ModalFooter>
        <Button variant="outline">Cancel</Button>
        <Button variant="primary">Submit</Button>
      </ModalFooter>
    </ModalDemo>
  ),
};

export const ExtraLargeModal: Story = {
  render: () => (
    <ModalDemo
      modalProps={{
        title: 'Extra Large Modal',
        size: 'xl',
      }}
      buttonText="Open XL Modal"
    >
      <ModalBody>
        <p>This is an extra large modal with even more space. Perfect for complex forms, data tables, or rich content.</p>
        <div style={{ marginTop: '1rem' }}>
          <h4>Extended Content</h4>
          <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
          <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>
        </div>
      </ModalBody>
      <ModalFooter>
        <Button variant="outline">Cancel</Button>
        <Button variant="primary">Save</Button>
      </ModalFooter>
    </ModalDemo>
  ),
};

export const FullScreenModal: Story = {
  render: () => (
    <ModalDemo
      modalProps={{
        title: 'Full Screen Modal',
        size: 'full',
      }}
      buttonText="Open Full Screen Modal"
    >
      <ModalBody>
        <p>This modal takes up the entire screen. It's useful for immersive experiences, complex workflows, or when you need maximum space.</p>
        <div style={{ marginTop: '2rem' }}>
          <h4>Full Screen Content</h4>
          <p>You have the entire screen to work with. This is perfect for:</p>
          <ul>
            <li>Complex forms with multiple sections</li>
            <li>Data visualization and dashboards</li>
            <li>Image or video editing interfaces</li>
            <li>Multi-step wizards</li>
            <li>Rich text editors</li>
          </ul>
        </div>
      </ModalBody>
      <ModalFooter>
        <Button variant="outline">Cancel</Button>
        <Button variant="primary">Save</Button>
      </ModalFooter>
    </ModalDemo>
  ),
};

// Content Examples
export const ConfirmationDialog: Story = {
  render: () => (
    <ModalDemo
      modalProps={{
        title: 'Delete Item',
        size: 'sm',
      }}
      buttonText="Delete Item"
    >
      <ModalBody>
        <p>Are you sure you want to delete this item? This action cannot be undone.</p>
      </ModalBody>
      <ModalFooter>
        <Button variant="outline">Cancel</Button>
        <Button variant="danger">Delete</Button>
      </ModalFooter>
    </ModalDemo>
  ),
};

export const FormModal: Story = {
  render: () => (
    <ModalDemo
      modalProps={{
        title: 'Create New User',
        size: 'md',
      }}
      buttonText="Create User"
    >
      <ModalBody>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
          <Input
            label="First Name"
            placeholder="Enter first name"
            required
          />
          <Input
            label="Last Name"
            placeholder="Enter last name"
            required
          />
          <Input
            label="Email"
            type="email"
            placeholder="Enter email address"
            required
          />
          <Input
            label="Password"
            type="password"
            placeholder="Enter password"
            required
            showPasswordToggle
          />
        </div>
      </ModalBody>
      <ModalFooter>
        <Button variant="outline">Cancel</Button>
        <Button variant="primary">Create User</Button>
      </ModalFooter>
    </ModalDemo>
  ),
};

export const ScrollableContent: Story = {
  render: () => (
    <ModalDemo
      modalProps={{
        title: 'Terms and Conditions',
        size: 'lg',
      }}
      buttonText="View Terms"
    >
      <ModalBody>
        <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
          <h4>1. Introduction</h4>
          <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
          
          <h4>2. User Responsibilities</h4>
          <p>Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
          
          <h4>3. Privacy Policy</h4>
          <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.</p>
          
          <h4>4. Terms of Service</h4>
          <p>Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>
          
          <h4>5. Limitation of Liability</h4>
          <p>Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium.</p>
          
          <h4>6. Governing Law</h4>
          <p>Totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt.</p>
        </div>
      </ModalBody>
      <ModalFooter>
        <Button variant="outline">Decline</Button>
        <Button variant="primary">Accept</Button>
      </ModalFooter>
    </ModalDemo>
  ),
};

// Theme Showcase
export const ThemeShowcase: Story = {
  render: () => (
    <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '1rem' }}>
      {(['glassmorphism', 'minimal', 'neumorphism', 'brutalist'] as const).map((theme) => (
        <ThemeProvider key={theme} initialTheme={theme}>
          <div style={{ textAlign: 'center' }}>
            <h4 style={{ marginBottom: '1rem', textTransform: 'capitalize' }}>{theme}</h4>
            <ModalDemo
              modalProps={{
                title: `${theme.charAt(0).toUpperCase() + theme.slice(1)} Modal`,
                size: 'md',
              }}
              buttonText={`Open ${theme} Modal`}
            >
              <ModalBody>
                <p>This modal showcases the {theme} theme with its unique styling and visual effects.</p>
                <div style={{ marginTop: '1rem' }}>
                  <Input
                    label="Sample Input"
                    placeholder="Try typing here"
                  />
                </div>
              </ModalBody>
              <ModalFooter>
                <Button variant="outline">Cancel</Button>
                <Button variant="primary">Save</Button>
              </ModalFooter>
            </ModalDemo>
          </div>
        </ThemeProvider>
      ))}
    </div>
  ),
  parameters: {
    layout: 'padded',
  },
};

// Nested Modals Example
export const NestedModals: Story = {
  render: () => {
    const NestedModalDemo = () => {
      const [firstModal, setFirstModal] = useState(false);
      const [secondModal, setSecondModal] = useState(false);

      return (
        <>
          <Button onClick={() => setFirstModal(true)}>Open First Modal</Button>
          
          <Modal
            isOpen={firstModal}
            onClose={() => setFirstModal(false)}
            title="First Modal"
            size="md"
          >
            <ModalBody>
              <p>This is the first modal. You can open another modal from here.</p>
            </ModalBody>
            <ModalFooter>
              <Button variant="outline" onClick={() => setFirstModal(false)}>Close</Button>
              <Button variant="primary" onClick={() => setSecondModal(true)}>Open Second Modal</Button>
            </ModalFooter>
          </Modal>
          
          <Modal
            isOpen={secondModal}
            onClose={() => setSecondModal(false)}
            title="Second Modal"
            size="sm"
          >
            <ModalBody>
              <p>This is a nested modal. It appears on top of the first modal.</p>
            </ModalBody>
            <ModalFooter>
              <Button variant="primary" onClick={() => setSecondModal(false)}>Close</Button>
            </ModalFooter>
          </Modal>
        </>
      );
    };

    return <NestedModalDemo />;
  },
};