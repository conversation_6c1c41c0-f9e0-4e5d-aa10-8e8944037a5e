import type { Theme } from './types';

export const brutalistTheme: Theme = {
  name: 'brutalist',
  colors: {
    primary: {
      50: '#fff7ed',
      100: '#ffedd5',
      200: '#fed7aa',
      300: '#fdba74',
      400: '#fb923c',
      500: '#f97316',
      600: '#ea580c',
      700: '#c2410c',
      800: '#9a3412',
      900: '#7c2d12',
    },
    secondary: {
      50: '#fef2f2',
      100: '#fee2e2',
      200: '#fecaca',
      300: '#fca5a5',
      400: '#f87171',
      500: '#ef4444',
      600: '#dc2626',
      700: '#b91c1c',
      800: '#991b1b',
      900: '#7f1d1d',
    },
    neutral: {
      50: '#ffffff',
      100: '#f5f5f5',
      200: '#e5e5e5',
      300: '#d4d4d4',
      400: '#a3a3a3',
      500: '#737373',
      600: '#525252',
      700: '#404040',
      800: '#262626',
      900: '#000000',
    },
    success: '#16a34a',
    warning: '#eab308',
    error: '#dc2626',
    info: '#2563eb',
    background: {
      primary: '#ffffff',
      secondary: '#000000',
      tertiary: '#f97316',
    },
    text: {
      primary: '#000000',
      secondary: '#404040',
      tertiary: '#737373',
      inverse: '#ffffff',
    },
    border: {
      light: '#000000',
      medium: '#000000',
      heavy: '#000000',
    },
  },
  spacing: {
    xs: '0.25rem',
    sm: '0.5rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '2rem',
    '2xl': '3rem',
    '3xl': '4rem',
    '4xl': '6rem',
  },
  typography: {
    fontFamily: {
      sans: 'Space Grotesk, Arial Black, "Arial Bold", Gadget, sans-serif',
      serif: 'Playfair Display, "Times New Roman Bold", Times, serif',
      mono: 'Space Mono, "Courier New Bold", Courier, monospace',
    },
    fontSize: {
      xs: '0.75rem',
      sm: '0.875rem',
      base: '1rem',
      lg: '1.125rem',
      xl: '1.25rem',
      '2xl': '1.5rem',
      '3xl': '1.875rem',
      '4xl': '2.25rem',
      '5xl': '3rem',
    },
    fontWeight: {
      light: 400,
      normal: 600,
      medium: 700,
      semibold: 800,
      bold: 900,
    },
    lineHeight: {
      tight: 1.1,
      normal: 1.3,
      relaxed: 1.5,
    },
  },
  shadows: {
    sm: '2px 2px 0px #000000',
    md: '4px 4px 0px #000000',
    lg: '6px 6px 0px #000000',
    xl: '8px 8px 0px #000000',
    '2xl': '12px 12px 0px #000000',
    inner: 'inset 2px 2px 0px #000000',
    none: 'none',
  },
  borderRadius: {
    none: '0',
    sm: '0',
    md: '0',
    lg: '0',
    xl: '0',
    '2xl': '0',
    full: '0',
  },
  effects: {
    blur: {
      sm: 'blur(0px)',
      md: 'blur(0px)',
      lg: 'blur(0px)',
      xl: 'blur(0px)',
    },
    backdrop: {
      blur: {
        sm: 'backdrop-blur(0px)',
        md: 'backdrop-blur(0px)',
        lg: 'backdrop-blur(0px)',
        xl: 'backdrop-blur(0px)',
      },
    },
    opacity: {
      10: '0.1',
      20: '0.2',
      30: '0.3',
      40: '0.4',
      50: '0.5',
      60: '0.6',
      70: '0.7',
      80: '0.8',
      90: '0.9',
    },
  },
};