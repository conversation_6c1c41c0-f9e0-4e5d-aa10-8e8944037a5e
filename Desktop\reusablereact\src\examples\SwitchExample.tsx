import React, { useState } from 'react';
import { Switch } from '../components/Switch';

export const SwitchExample = () => {
  const [isEnabled, setIsEnabled] = useState(false);
  const [darkMode, setDarkMode] = useState(false);
  const [notifications, setNotifications] = useState(true);
  
  return (
    <div className="switch-examples">
      <h2>Switch Component Examples</h2>
      
      <div className="example-section">
        <h3>Basic Switch</h3>
        <Switch 
          label="Enable feature" 
          checked={isEnabled}
          onChange={(e) => setIsEnabled(e.target.checked)}
        />
        <p>Feature is {isEnabled ? 'enabled' : 'disabled'}</p>
      </div>
      
      <div className="example-section">
        <h3>Switch with Helper Text</h3>
        <Switch 
          label="Dark mode" 
          helperText="Enable dark mode for the application"
          checked={darkMode}
          onChange={(e) => setDarkMode(e.target.checked)}
        />
      </div>
      
      <div className="example-section">
        <h3>Switch with <PERSON>rror</h3>
        <Switch 
          label="Notifications" 
          error={!notifications ? "Notifications are required for this app" : undefined}
          checked={notifications}
          onChange={(e) => setNotifications(e.target.checked)}
        />
      </div>
      
      <div className="example-section">
        <h3>Switch Sizes</h3>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
          <Switch label="Small switch" size="sm" />
          <Switch label="Medium switch" size="md" />
          <Switch label="Large switch" size="lg" />
        </div>
      </div>
      
      <div className="example-section">
        <h3>Disabled Switch</h3>
        <Switch label="Disabled switch" disabled />
        <Switch label="Disabled checked switch" disabled checked />
      </div>
    </div>
  );
};